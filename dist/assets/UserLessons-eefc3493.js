import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{k as L,r as o,f as P,b as k}from"./vendor-851db8c1.js";import{G as q,u as E,h as F,T as I,M as G}from"./index-97f6f167.js";import{F as _,a as A,C as B}from"./FindByTimeTab-6ce8621d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index.esm-b72032a7.js";import"./BottomDrawer-f3121b82.js";import"./AddPlayers-4c2a1b90.js";import"./react-tooltip-7a26650a.js";import"./BackButton-11ba52b2.js";import"./ReservationSummary-c649f1d8.js";import"./TimeSlots-9ce7c837.js";import"./Calendar-282b3fcf.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SportTypeSelection-cc97819b.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./CalendarIcon-b3488133.js";let n=new I,D=new G;function It(){const[x]=L(),l=x.get("coach"),[r,u]=o.useState("coach"),[i,h]=o.useState([]),[a,f]=o.useState([]),{dispatch:y}=o.useContext(q),[g,d]=o.useState(!0),[m,b]=o.useState([]),[c,w]=o.useState(null),j=P(),v=[{id:"coach",label:"Find by coach"},{id:"time",label:"Find by time"},{id:"custom",label:"Custom request"}],{club:t,sports:p}=E(),S=localStorage.getItem("user"),C=async()=>{try{const s=await n.getOne("user",S,{});w(s.model)}catch(s){console.error(s)}},T=async()=>{const s=await n.getList("coach",{join:["user|user_id"],filter:[`courtmatchup_coach.club_id,eq,${parseInt(t==null?void 0:t.id)}`]}),R=await n.getList("user",{filter:["role,cs,user",`club_id,eq,${parseInt(t==null?void 0:t.id)}`]});h(s.list),f(R.list)},N=async()=>{try{const s=await D.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");b(s.groups)}catch(s){console.error(s)}};return o.useEffect(()=>{(async()=>t!=null&&t.id&&(d(!0),await C(),await T(),await N(),d(!1)))()},[t==null?void 0:t.id]),k.useEffect(()=>{y({type:"SETPATH",payload:{path:"lessons"}}),l&&u("coach")},[l]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white px-3 py-3 sm:px-4 sm:py-4",children:[g&&e.jsx(F,{}),e.jsx("h1",{className:"mb-4 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Lessons"}),e.jsxs("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0",children:[e.jsx("div",{className:"flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-0 sm:text-sm",children:v.map(s=>e.jsx("button",{onClick:()=>u(s.id),className:`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${r===s.id?"bg-white-600 font-medium":"bg-gray-100 text-gray-600"}`,children:s.label},s.id))}),r==="custom"&&e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>j("/user/create-custom-request"),className:"rounded-lg bg-primaryBlue px-3 py-1.5 text-sm text-white transition-colors hover:bg-blue-600 sm:px-4 sm:py-2",children:"Create request"})})]})]}),e.jsxs("div",{className:"mx-auto max-w-7xl",children:[r==="coach"&&e.jsx(_,{sports:p,coaches:i,players:a,groups:m,club:t,userProfile:c}),r==="time"&&e.jsx(A,{sports:p,coaches:i,players:a,groups:m,club:t,userProfile:c}),r==="custom"&&e.jsx(B,{sports:p,coaches:i,players:a,groups:m,club:t,userProfile:c})]})]})}export{It as default};
