import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as v,r as C,f as M,L as A}from"./vendor-851db8c1.js";import{u as O}from"./react-hook-form-687afde5.js";import{o as $}from"./yup-2824f222.js";import{c as q,a as i,e as D}from"./yup-5f77b7d2.js";import{A as I,G as Z,d as K,M as T,b as z,t as H}from"./index-97f6f167.js";import{B as J,a as Q}from"./index.esm-b72032a7.js";import{A as V}from"./AuthLayout-0cc499df.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Me=()=>{var d,m,c;const{dispatch:E}=v.useContext(I),[_,r]=C.useState(!1),[a,k]=C.useState(!1),S=window.location.search,P=new URLSearchParams(S).get("token"),{dispatch:B}=v.useContext(Z),F=q({code:i().required(),password:i().required(),confirmPassword:i().oneOf([D("password"),null],"Passwords must match")}).required(),R=M(),{register:o,handleSubmit:U,setError:n,formState:{errors:t}}=O({resolver:$(F)}),G=async x=>{var p,f,u,h,g,w,j,y;let L=new T;try{r(!0);const s=await L.reset(P,x.code,x.password);if(!s.error)z(B,"Password Reset successfully",3e3,"success"),R("/user/login");else if(s.validation){const b=Object.keys(s.validation);for(let l=0;l<b.length;l++){const N=b[l];n(N,{type:"manual",message:s.validation[N]})}}r(!1)}catch(s){r(!1),console.log("Error",s),n("code",{type:"manual",message:(f=(p=s==null?void 0:s.response)==null?void 0:p.data)!=null&&f.message?(h=(u=s==null?void 0:s.response)==null?void 0:u.data)==null?void 0:h.message:s==null?void 0:s.message}),H(E,(w=(g=s==null?void 0:s.response)==null?void 0:g.data)!=null&&w.message?(y=(j=s==null?void 0:s.response)==null?void 0:j.data)==null?void 0:y.message:s==null?void 0:s.message)}};return e.jsx(V,{children:e.jsx("div",{className:"flex min-h-screen flex-col bg-white",children:e.jsx("main",{className:"flex flex-grow flex-col bg-white",children:e.jsx("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("section",{className:"mt-6 flex w-full justify-center max-md:max-w-full",children:e.jsx("div",{className:"flex w-[553px] min-w-[240px] flex-col items-center pt-12",children:e.jsxs("div",{className:"flex w-[392px] max-w-full flex-col",children:[e.jsxs("div",{className:"mb-4 flex w-full flex-col",children:[e.jsx("div",{className:"flex w-[74px] items-start gap-4 self-center overflow-hidden rounded-[96px] p-2",children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_139_24350)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_139_24350)"}),e.jsxs("g",{filter:"url(#filter0_d_139_24350)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M24.5 37C24.5 33.6024 25.8556 30.5213 28.0554 28.2682C30.817 30.187 32.625 33.3824 32.625 37C32.625 40.6176 30.817 43.813 28.0554 45.7318C25.8556 43.4787 24.5 40.3976 24.5 37Z",fill:"#176448"}),e.jsx("path",{d:"M34.5 37C34.5 32.9105 32.5361 29.2796 29.5 26.9991C31.5892 25.4299 34.186 24.5 37 24.5C39.814 24.5 42.4109 25.4299 44.5 26.9991C41.4639 29.2796 39.5 32.9105 39.5 37C39.5 41.0895 41.4639 44.7204 44.5 47.0009C42.4109 48.5701 39.814 49.5 37 49.5C34.186 49.5 31.5892 48.5701 29.5 47.0009C32.5361 44.7204 34.5 41.0895 34.5 37Z",fill:"#176448"}),e.jsx("path",{d:"M45.9446 28.2683C48.1444 30.5213 49.5 33.6024 49.5 37C49.5 40.3976 48.1444 43.4787 45.9446 45.7317C43.183 43.813 41.375 40.6176 41.375 37C41.375 33.3824 43.183 30.187 45.9446 28.2683Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_139_24350",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_139_24350"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_139_24350",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_139_24350",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_139_24350",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("h1",{className:" mt-4 w-full text-center text-2xl font-medium leading-none text-gray-950",children:"Reset Password"}),e.jsx("p",{className:"mt-2 text-center text-gray-500",children:"Enter your new password"})]}),e.jsxs("form",{onSubmit:U(G),className:"w-full space-y-4",children:[e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"mb-1.5 block text-sm font-medium text-gray-700",children:"Code"}),e.jsx("input",{type:"number",className:`block w-full rounded-lg border ${t.code?"border-red-300":"border-gray-300"} bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500`,...o("code")}),e.jsx("span",{className:"text-red-400",children:(d=t.code)==null?void 0:d.message})]}),e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"mb-1.5 block text-sm font-medium text-gray-700",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:a?"text":"password",className:`block w-full rounded-lg border ${t.password?"border-red-300":"border-gray-300"} bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500`,...o("password")}),e.jsx("button",{type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2 transform",onClick:()=>k(!a),children:a?e.jsx(J,{className:"text-gray-500"}):e.jsx(Q,{className:"text-gray-500"})})]}),e.jsx("span",{className:"text-red-400",children:(m=t.password)==null?void 0:m.message})]}),e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"mb-1.5 block text-sm font-medium text-gray-700",children:"Confirm Password"}),e.jsx("input",{type:"password",className:`block w-full rounded-lg border ${t.confirmPassword?"border-red-300":"border-gray-300"} bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500`,...o("confirmPassword")}),e.jsx("span",{className:"text-red-400",children:(c=t.confirmPassword)==null?void 0:c.message})]}),e.jsx(K,{type:"submit",loading:_,className:"mt-6 w-full gap-2.5 self-stretch overflow-hidden whitespace-nowrap rounded-xl bg-emerald-800 px-2.5 py-3 text-center text-lg font-medium leading-none tracking-tight text-white shadow-sm",children:"Reset Password"}),e.jsx("div",{className:"mt-4 ",children:e.jsx(A,{to:"/club/login",className:"text-sm font-medium text-gray-500 underline",children:"Back to Login"})})]})]})})})})})})})};export{Me as default};
