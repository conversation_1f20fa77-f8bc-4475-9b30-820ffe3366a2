import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,f as ye,L as ue,b as we}from"./vendor-851db8c1.js";import{$ as ie,G as fe,aD as xe,c as Ne,x as C,d as Se,aE as be,M as ge,b as q,as as _e,o as Re,h as Ce,T as ke}from"./index-97f6f167.js";import{T as Pe}from"./TimeSlots-9ce7c837.js";import{S as Be}from"./SportTypeSelection-cc97819b.js";import{C as Me}from"./Calendar-282b3fcf.js";import{b as Te}from"./index.esm-b72032a7.js";import{A as Ee}from"./AddPlayers-4c2a1b90.js";import{B as Ae}from"./BottomDrawer-f3121b82.js";import{B as $e}from"./BackButton-11ba52b2.js";import{L as pe}from"./ReservationSummary-c649f1d8.js";import{f as Le}from"./date-fns-07266b7d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";function Ie({title:d,titleId:c,...m},u){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:u,"aria-labelledby":c},m),d?r.createElement("title",{id:c},d):null,r.createElement("path",{fillRule:"evenodd",d:"M4.25 12a.75.75 0 0 1 .75-.75h14a.75.75 0 0 1 0 1.5H5a.75.75 0 0 1-.75-.75Z",clipRule:"evenodd"}))}const De=r.forwardRef(Ie),Fe=De;function Oe({title:d,titleId:c,...m},u){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:u,"aria-labelledby":c},m),d?r.createElement("title",{id:c},d):null,r.createElement("path",{fillRule:"evenodd",d:"M12 3.75a.75.75 0 0 1 .75.75v6.75h6.75a.75.75 0 0 1 0 1.5h-6.75v6.75a.75.75 0 0 1-1.5 0v-6.75H4.5a.75.75 0 0 1 0-1.5h6.75V4.5a.75.75 0 0 1 .75-.75Z",clipRule:"evenodd"}))}const qe=r.forwardRef(Oe),He=qe,Ge=new ge;function Ve({coaches:d,onClose:c,selectedDate:m,selectedSport:u,timeRange:y,sports:k,players:p,groups:P,club:n,isOpen:oe,selectedType:w,selectedSubType:N,selectedTimes:h,userProfile:B,notes:f,numberOfPlayers:S}){const[g,H]=r.useState(!0),[t,G]=r.useState(null),[M,b]=r.useState(""),[j,_]=r.useState(1),[x,T]=r.useState([]),[E,A]=r.useState(1),[$,L]=r.useState(!1),[V,Z]=r.useState(null),[Y,I]=r.useState(!1),[i,J]=r.useState(0),[D,U]=r.useState(0),z=ye(),{duration:v,end_time:K,start_time:Q}=ie(h),[le,de]=r.useState(null),[W,je]=r.useState(null),[X,me]=r.useState(null),{dispatch:R}=r.useContext(fe),[F,ce]=r.useState(null),ee=()=>{A(s=>Math.min(s+1,10))},se=()=>{A(s=>Math.max(s-1,1))},te=async()=>{if(!u||!m||!h||!x){q(R,"Please select all required fields",3e3,"error");return}I(!0);try{const s={sport_id:u,type:w,sub_type:N,date:m,player_ids:x.map(o=>o.id),start_time:Q,end_time:K,duration:v,coach_id:t?t.id:null,payment_intent:null,custom_request:1,notes:f,num_needed:S,reservation_type:_e.coach},a=await Ge.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",s,"POST");a.error||(q(R,"Request created successfully",3e3,"success"),ce(a.reservation_id),me(a.booking_id),z("/user/lessons"))}catch(s){console.error(s),q(R,s.message||"Error creating reservation",3e3,"error")}finally{I(!1)}},re=s=>{T(a=>a.some(l=>l.id===s.id)?a.filter(l=>l.id!==s.id):[...a,s])},ae=()=>{t&&_(2)};r.useEffect(()=>{if(v&&(t!=null&&t.hourly_rate)){const s=v*(t==null?void 0:t.hourly_rate),a=xe(n==null?void 0:n.fee_settings,s),o=(n==null?void 0:n.club_fee)||0;J(s+a+o),U(s)}},[v,t,n==null?void 0:n.fee_settings,n==null?void 0:n.club_fee]);const ne=n!=null&&n.lesson_description?JSON.parse(n==null?void 0:n.lesson_description):{reservation_description:"",payment_description:""};return e.jsx(Ae,{onClose:c,isOpen:oe,title:j===1?"Select Coach":"Reservation detail",children:e.jsxs("div",{className:"relative mx-auto h-[90vh] w-full max-w-6xl overflow-y-auto rounded-lg bg-white p-6",children:[e.jsx($e,{onBack:()=>{}}),j===1&&e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:M,onChange:s=>b(s.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(Ne,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>H(!g),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(Te,{className:`text-xs transition-transform ${g?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto",children:[d.length>0&&d.filter(s=>`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`.toLowerCase().includes(M.toLowerCase())).sort((s,a)=>{const o=`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`.toLowerCase(),l=`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`.toLowerCase();return g?o.localeCompare(l):l.localeCompare(o)}).map(s=>e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(t==null?void 0:t.id)===s.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>G(s),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:(s==null?void 0:s.photo)||(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s==null?void 0:s.first_name} ${s==null?void 0:s.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[s==null?void 0:s.first_name," ",s==null?void 0:s.last_name]})})]}),e.jsxs("span",{className:"text-gray-600",children:[C(s.hourly_rate),"/h"]})]},s.id)),e.jsx("div",{className:"flex h-full flex-col items-center justify-center gap-4",children:e.jsx("button",{onClick:()=>{G(null),_(2)},className:"b rounded-lg border border-primaryBlue px-4 py-2 text-sm text-primaryBlue ",children:"Continue without coach"})})]})]}),t&&e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Coach Profile"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-16 w-16 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsxs("p",{className:"text-lg text-gray-600",children:[C(t==null?void 0:t.hourly_rate),"/h"]})]})]}),(t==null?void 0:t.bio)&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:t==null?void 0:t.bio})})]}),e.jsx("div",{className:"border-t p-3",children:e.jsxs("button",{onClick:ae,className:"rounded-lg bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:["Continue with ",t==null?void 0:t.first_name]})})]})]}),j===2&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(pe,{selectedSport:u,sports:k,selectedType:w,selectedSubType:N,selectedDate:m,selectedTimes:h,playersNeeded:E,selectedCoach:t,timeRange:ie(h)}),e.jsx(Ee,{searchQuery:M,setSearchQuery:b,selectedPlayers:x,setSelectedPlayers:T,onPlayerToggle:re,players:p,groups:P,selectedGroup:V,isFindBuddyEnabled:$,setIsFindBuddyEnabled:L,playersNeeded:E,handleIncrement:ee,handleDecrement:se,showAddReservationToFindBuddy:!1,showPlayersNeeded:!1}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving Details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",x.length,")"]}),e.jsx("div",{className:"mt-1",children:x.map(s=>s.id===B.id?e.jsx("div",{className:"text-sm",children:"Me"},s.id):e.jsxs("div",{className:"text-sm",children:[s.first_name," ",s.last_name]},s.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsx("div",{className:"text-sm",children:t?`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`:"No coach selected"})})]})]}),e.jsx(Se,{loading:Y,onClick:te,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Send request"}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:ne.reservation_description}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]}),j===3&&e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(pe,{selectedSport:u,sports:k,selectedType:w,selectedSubType:N,selectedDate:m})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:C((n==null?void 0:n.club_fee)||0)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:C(xe(n==null?void 0:n.fee_settings,D))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:C(i)})]}),e.jsx("div",{children:e.jsx(be,{user:B,bookingId:X,clientSecret:le,paymentIntent:W,reservationId:F,navigateRoute:`/user/payment-success/${F}?type=lesson`})}),e.jsxs("div",{className:"space-y-4 text-sm text-gray-500",children:[e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(ue,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(ue,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})]})})]})]})]})})}let O=new ge,he=new ke;function Is(){const[d,c]=r.useState(null),[m,u]=r.useState(null),[y,k]=r.useState(null),[p,P]=r.useState(new Date),[n,oe]=r.useState([]),[w,N]=r.useState(null),[h,B]=r.useState(null),[f,S]=r.useState(5),[g,H]=r.useState(""),[t,G]=r.useState(0),[M,b]=r.useState(0),[j,_]=r.useState(!1),[x,T]=r.useState([]),[E,A]=r.useState([]),[$,L]=r.useState(!1),[V,Z]=r.useState(!1),[Y,I]=r.useState(null),[i,J]=r.useState(null),[D,U]=r.useState([]),[z,v]=r.useState([]),[K,Q]=r.useState([]),{state:le,dispatch:de}=r.useContext(fe),[W,je]=r.useState({from:null,until:null}),[X,me]=r.useState("main"),{start_time:R,end_time:F,duration:ce}=ie(x),ee=localStorage.getItem("user"),se=async()=>{try{const a=await O.callRawAPI("/v2/api/lambda/preference",{},"GET"),o=await he.getOne("user",ee,{}),l=await O.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${o.model.club_id}`,{},"GET"),ve=await he.getList("user",{filter:["role,cs,user",`club_id,cs,${l.model.id}`]});v(ve.list),U(l.sports),I(a),J(l.model)}catch(a){console.error(a)}},te=async()=>{try{const a=await O.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");Q(a.groups)}catch(a){console.error(a)}};r.useEffect(()=>{(async()=>(Z(!0),await se(),await te(),Z(!1)))()},[]),r.useEffect(()=>{},[X]);const re=()=>{P(new Date(p.setMonth(p.getMonth()-1)))},ae=()=>{P(new Date(p.setMonth(p.getMonth()+1)))},ne=a=>{T([{from:a.from,until:a.until}])};r.useEffect(()=>{t&&(n!=null&&n.length)?b(t*(n==null?void 0:n.length)):b(t)},[t,n]);const s=async()=>{_(!0);const a={start_time:R,sport_id:d,end_time:F,date:Le(new Date(y),"yyyy-MM-dd")};try{const o=await O.callRawAPI("/v3/api/custom/courtmatchup/user/coach/search-time-slots",a,"POST");o.error||(A(o.list),L(!0))}catch(o){console.error("ERROR",o),q(de,o.message,5e3)}finally{_(!1)}};return we.useEffect(()=>{Re({path:"/user/create-custom-request",clubName:i==null?void 0:i.name,favicon:i==null?void 0:i.club_logo,title:"Custom request",description:"Custom Request"})},[i==null?void 0:i.club_logo]),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"flex items-center justify-center bg-white p-4 shadow-sm",children:e.jsx("p",{children:"Custom Request"})}),V&&e.jsx(Ce,{}),e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:" p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"flex items-center justify-center rounded-lg bg-gray-50 p-3 text-center",children:"Summary"}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Number of players"}),e.jsxs("div",{className:"flex max-w-fit items-center gap-2 rounded-xl border border-gray-300",children:[e.jsx("button",{onClick:()=>S(f-1),className:"flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50",children:e.jsx(Fe,{className:"h-4 w-4"})}),e.jsx("input",{type:"number",className:"w-8 rounded-lg border-none p-0 text-center text-gray-700",value:f,min:1,onChange:a=>S(a.target.value)}),e.jsx("button",{onClick:()=>S(f+1),className:"flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50",children:e.jsx(He,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("p",{className:"text-sm text-gray-600",children:"Custom requests are meant for parties of 5 or move players. The requests made will be checked by the club and the club will respond to you over the registered email address."})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-gray-900",children:["Custom request"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsx("textarea",{className:"w-full rounded-xl border border-gray-300 p-2 text-sm",placeholder:"Add a note",rows:4,onChange:a=>H(a.target.value),value:g,maxLength:200,showCount:!0})]})]}),e.jsx(Be,{sports:D,onSelectionChange:({sport:a,type:o,subType:l})=>{c(a),N(o),B(l)},isChildren:!0})]})]}),e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsx(Me,{currentMonth:p,selectedDate:y,onDateSelect:k,onPreviousMonth:re,onNextMonth:ae,daysOff:i!=null&&i.days_off?JSON.parse(i.days_off):[]})}),e.jsx(Pe,{isLoading:j,selectedDate:y,timeRange:x,onTimeClick:ne,onNext:s,nextButtonText:"Next: Select coach",startHour:0,endHour:24,interval:30,clubTimes:i!=null&&i.times?JSON.parse(i.times):[],isTimeSlotAvailable:a=>!0})]})})})}),$&&e.jsx(Ve,{coaches:E,onClose:()=>L(!1),selectedDate:y,selectedSport:d,selectedLocation:m,timeRange:W,sports:D,players:z,groups:K,club:i,isOpen:$,selectedTimes:x,selectedPlayers:n,selectedType:w,selectedSubType:h,userProfile:Y,notes:g,numberOfPlayers:f})]})]})}export{Is as default};
