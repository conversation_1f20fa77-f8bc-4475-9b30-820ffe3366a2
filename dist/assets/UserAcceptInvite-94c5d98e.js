import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{j as f,r as t}from"./vendor-851db8c1.js";import{M as g}from"./index-97f6f167.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let m=new g;function X(){const{id:c}=f();t.useState(1);const[l,r]=t.useState(!1),[i,s]=t.useState(null),[o,p]=t.useState(null);t.useState([]);const u=async()=>{r(!0),s(null),p(null);try{const a=(await m.callRawAPI("/v3/api/custom/courtmatchup/user/groups/pending-invites",{},"GET")).invites.find(v=>v.group_id===parseInt(c));console.log("currentInvite",a);const d={invite_id:a.invite_id,status:"accepted"};await m.callRawAPI("/v3/api/custom/courtmatchup/user/groups/invite-response",d,"POST"),s("Invite accepted successfully!")}catch(n){p("Error accepting invite: "+n.message)}finally{r(!1)}};return t.useEffect(()=>{u()},[]),e.jsxs("div",{className:"flex flex-col items-center justify-center p-4",children:[l&&e.jsx("p",{className:"text-blue-500",children:"Loading..."}),i&&e.jsx("p",{className:"text-green-500",children:i}),o&&e.jsx("p",{className:"text-red-500",children:o})]})}export{X as default};
