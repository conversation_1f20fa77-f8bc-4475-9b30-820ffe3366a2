import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as i}from"./vendor-851db8c1.js";import{u as Le}from"./react-hook-form-687afde5.js";import{o as $e}from"./yup-2824f222.js";import{c as Me}from"./yup-5f77b7d2.js";import{M as Be,A as ze,G as Oe,B as qe,L as C,bb as te,t as $,g as Ge}from"./index-97f6f167.js";import{P as He}from"./index-eb1bc208.js";import{T as _e}from"./index-2a2554ab.js";import{S as Ke}from"./index-02625b16.js";import{A as Ue}from"./index.esm-9c6194ba.js";import{a as We}from"./index.esm-c561e951.js";import{A as Ye}from"./AddButton-df0c3574.js";import Je from"./MkdListTableHead-8984d225.js";import Qe from"./MkdListTableRow-aa8d3276.js";import{M as Xe}from"./index-f38a1caa.js";import"./index-0047164a.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./AddButton.module-98aac587.js";const Ze=({onClick:n,className:a})=>e.jsx(e.Fragment,{children:e.jsxs("button",{onClick:n,className:`relative flex h-[2.125rem] w-fit min-w-fit  items-center justify-center overflow-hidden rounded-md border border-primaryBlue bg-indigo-600 px-[.6125rem]  py-[.5625rem] font-['Inter'] text-sm font-medium leading-none text-white shadow-md shadow-indigo-600 ${a}`,children:[e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),e.jsx("span",{children:"Export"})]})});let m=new Be;const Jt=({columns:n=[],actions:a={view:{show:!0,multiple:!0,action:null},edit:{show:!0,multiple:!0,action:null},delete:{show:!0,multiple:!0,action:null},select:{show:!0,multiple:!0,action:null},add:{show:!0,multiple:!0,action:null,showChildren:!0,children:"Add New"},export:{show:!0,multiple:!0,action:null}},actionPostion:E="ontable",actionId:M="id",table:p,tableRole:se,tableTitle:A="",hasFilter:re=!0,schemaFields:Ve=[],showPagination:et=!0,refreshRef:B=null})=>{var Q,X,Z,V;const{dispatch:T}=i.useContext(ze),[j,y]=i.useState([]),[d,z]=i.useState(10),[le,ae]=i.useState(0),[D,ie]=i.useState(0),[oe,ne]=i.useState(!1),[de,ce]=i.useState(!1),[ue,v]=i.useState(!1),[me,k]=i.useState(!1),[O,q]=i.useState(!1),[G,H]=i.useState(!1),[h,P]=i.useState([]),[_,F]=i.useState([]),[b,x]=i.useState([]),[pe,he]=i.useState("eq"),[K,R]=i.useState(!0),I=i.useRef(null),fe=Me({}),[U,W]=i.useState(null),[Y,N]=i.useState(!1),[f,g]=i.useState([]),{state:xe,dispatch:ge}=i.useContext(Oe);function we(t){var l;const s=f;if((l=a==null?void 0:a.select)!=null&&l.multiple)if(s.includes(t)){const r=s.filter(o=>o!==t);g(()=>[...r]),x(r)}else{const r=[...s,t];g(()=>[...r]),x(r)}else if(s.includes(t)){const r=s.filter(o=>o!==t);g(()=>[...r]),x(r)}else{const r=[t];g(()=>[...r]),x(r)}}const je=()=>{if(N(t=>!t),Y)g([]),x([]);else{const t=j.map(s=>s[M]);g(t),x(t)}},be=async t=>{v(!0),W(t)};i.useEffect(()=>{f.length<=0&&N(!1),f.length===j.length&&N(!0),f.length<j.length&&f.length>0&&N(!1)},[f,j]);const{handleSubmit:ye,reset:tt}=Le({resolver:$e(fe)});function ve(t){n[t].isSorted?n[t].isSortedDesc=!n[t].isSortedDesc:(n.map(s=>s.isSorted=!1),n.map(s=>s.isSortedDesc=!1),n[t].isSorted=!0),async function(){await u(0,d)}()}function Ne(){u(D-1,d)}function Se(){u(D+1,d)}const Ce=(t,s,l)=>{const r=s==="eq"&&isNaN(l)?`${l}`:l,o=`${t},${s},${r}`;F(c=>[...c.filter(w=>!w.includes(t)),o])},Ee=()=>{console.log("filterConditions",_),u(0,d,{},_)},Ae=t=>{u(0,d,{},t)};async function u(t,s,l={},r=[]){R(!0);try{m.setTable(p);const o=await m.callRestAPI({payload:{...l},page:t,limit:s,filter:r},"PAGINATE");o&&R(!1);const{list:c,total:ee,limit:w,num_pages:S,page:L}=o;y(c),z(w),ae(S),ie(L),ne(L>1),ce(L+1<=S)}catch(o){R(!1),console.log("ERROR",o),$(T,o.message)}}async function Te(t,s,l={},r=[]){m.setTable(p);const o=await m.callRestAPI({payload:{...l},page:t,limit:s,filter:r},"PAGINATE"),{list:c}=o;y(c),ge({type:"REFRESH_DATA",payload:{refreshData:!1}})}const De=async t=>{async function s(l){try{k(!0),m.setTable(p);const r=await m.callRestAPI({id:l},"DELETE");r!=null&&r.error||(y(o=>o.filter(c=>Number(c.id)!==Number(l))),k(!1),v(!1))}catch(r){throw k(!1),v(!1),$(T,r==null?void 0:r.message),new Error(r)}}typeof t=="object"?t.forEach(async l=>{await s(l)}):typeof t=="number"&&await s(t)},ke=async t=>{try{m.setTable(p);const s=await m.exportCSV()}catch(s){throw new Error(s)}},Pe=t=>{const s=n.filter(l=>l.accessor).map(l=>{const r=Ge(t[l.accessor]);return r?`${l.accessor},cs,${r}`:null}).filter(Boolean);u(0,d,{},s)};async function Fe(t,s,l){try{m.setTable(p);const r=await m.callRestAPI({id:t,[s]:l},"PUT")}catch(r){console.log("ERROR",r),$(T,r.message)}}const Re=()=>{P([]),F([]),u(1,d)};async function Ie(t,s,l,r){let o;s=isNaN(Number.parseInt(s))?s:Number.parseInt(s);try{clearTimeout(o),o=setTimeout(async()=>{await Fe(t,r,s)},200),y(c=>c.map((w,S)=>S===l?{...w,[r]:s}:w))}catch(c){console.error(c)}}i.useEffect(()=>{var t;(t=a==null?void 0:a.select)!=null&&t.action&&a.select.action()},[b.length]),i.useEffect(()=>{const s=setTimeout(async()=>{await u(1,d)},700);return()=>{clearTimeout(s)}},[]),i.useEffect(()=>{Te(1,d)},[xe.refreshData]);const J=t=>{I.current&&!I.current.contains(t.target)&&q(!1)};return i.useEffect(()=>(document.addEventListener("mousedown",J),()=>{document.removeEventListener("mousedown",J)}),[]),e.jsxs("div",{className:"px-8",children:[B&&e.jsx("button",{ref:B,onClick:()=>u(1,d),className:"hidden"}),e.jsxs("div",{className:`flex gap-3 ${A?"flex-col":"h-fit items-center"}`,children:[re?e.jsx("div",{className:"flex w-auto items-center justify-between ",children:e.jsx("form",{className:"relative rounded bg-white",onSubmit:ye(Pe),children:e.jsx("div",{className:"flex items-center gap-4 text-nowrap text-gray-700",children:e.jsxs("div",{className:"relative",ref:I,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>q(!O),children:[e.jsx(qe,{}),e.jsx("span",{children:"Filters"}),h.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:h.length})]}),O&&e.jsx("div",{className:"top-fill filter-form-holder absolute left-0 z-10 mt-4 w-[500px] min-w-[90%] rounded-md border border-gray-200 bg-white shadow-lg",children:e.jsxs("div",{className:"p-4",children:[h==null?void 0:h.map((t,s)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:l=>{he(l.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:l=>Ce(t,pe,l.target.value),onKeyDown:l=>{l.key==="Enter"&&l.preventDefault()}}),e.jsx("div",{className:"mt-[-10px] w-1/12",children:e.jsx(We,{className:" cursor-pointer text-xl",onClick:()=>{P(l=>l.filter(r=>r!==t)),F(l=>{const r=l.filter(o=>!o.includes(t));return Ae(r),r})}})})]},s)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{H(!G)},children:[e.jsx(Ue,{}),"Add filter"]}),G&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:n.slice(0,-1).map(t=>e.jsx("li",{className:`${h.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{h.includes(t.accessor)||P(s=>[...s,t.accessor]),H(!1)},children:t.header},t.accessor))})}),h.length>0&&e.jsx("div",{onClick:Re,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:Ee,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})})}):null,e.jsxs("div",{className:"flex h-fit w-full justify-between text-center",children:[e.jsx("h4",{className:"text-2xl font-medium capitalize",children:A||""}),e.jsxs("div",{className:"flex h-full gap-2",children:[b!=null&&b.length&&E==="abovetable"?e.jsx(C,{children:e.jsx(_e,{actions:a,selectedItems:b})}):null,((Q=a==null?void 0:a.export)==null?void 0:Q.show)&&e.jsx(Ze,{showText:!1,onClick:ke,className:"mx-1"}),((X=a==null?void 0:a.add)==null?void 0:X.show)&&e.jsx(Ye,{onClick:()=>{var t,s;(t=a==null?void 0:a.add)!=null&&t.action&&((s=a==null?void 0:a.add)==null||s.action())},showChildren:(Z=a==null?void 0:a.add)==null?void 0:Z.showChildren,children:(V=a==null?void 0:a.add)==null?void 0:V.children})]})]})]}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 px-0",children:e.jsxs(e.Fragment,{children:[e.jsx("div",{className:`${K?"":"overflow-x-auto"} border-b border-gray-200 shadow`,children:K?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(Ke,{})}):e.jsx(e.Fragment,{children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx(C,{children:e.jsx(Je,{onSort:ve,columns:n,actions:a,actionPostion:E,areAllRowsSelected:Y,handleSelectAll:je})})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:j.map((t,s)=>e.jsx(C,{children:e.jsx(Qe,{i:s,row:t,columns:n,actions:a,actionPostion:E,actionId:M,handleTableCellChange:Ie,handleSelectRow:we,selectedIds:f,setDeleteId:be,table:p,tableRole:se},s)},s))})]})})}),e.jsx(C,{children:e.jsx(Xe,{open:ue,actionHandler:()=>{De(U)},closeModalFunction:()=>{W(null),v(!1)},title:`Delete ${te(p)} `,message:`You are about to delete ${te(p)} ${U}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:me})})]})}),e.jsx(He,{currentPage:D,pageCount:le,pageSize:d,canPreviousPage:oe,canNextPage:de,updatePageSize:t=>{z(t),u(1,t)},previousPage:Ne,nextPage:Se})]})};export{Jt as default};
