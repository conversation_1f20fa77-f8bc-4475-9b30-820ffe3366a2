import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{f as $,r as u,b as d}from"./vendor-851db8c1.js";import{_ as R,Z as h,a3 as k,x as g,M as U,G as q,h as cs,b as V,A as ds,R as Ss,ab as ks,T as Es,U as Ms,X as Ts,Y as ss,t as ws}from"./index-97f6f167.js";import{c as Ps,a as A}from"./yup-5f77b7d2.js";import{u as Ls}from"./react-hook-form-687afde5.js";import{o as Ds}from"./yup-2824f222.js";import{P as $s}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Rs from"./Skeleton-1e8bf077.js";import{L as As}from"./LoadingOverlay-87926629.js";import{R as B,T as Y,P as I,F as O,G as as,L as is}from"./ReservationStatus-0d38d99f.js";import{h as D}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";function Bs({reservation:e,club:N,clubSports:a,players:t}){var j;const i=$(),p=R(e==null?void 0:e.reservation_updated_at);return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{children:(e==null?void 0:e.booking_status)===h.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(B,{}),s.jsx(Y,{timeLeft:p})]})||(e==null?void 0:e.booking_status)===h.SUCCESS&&s.jsx(I,{})||(e==null?void 0:e.booking_status)===h.FAIL&&s.jsx(O,{})})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(j=a==null?void 0:a.find(c=>c.id===(e==null?void 0:e.sport_id)))==null?void 0:j.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:D(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[k(e==null?void 0:e.start_time)," -"," ",k(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t.map(c=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:c.photo||"/default-avatar.png",alt:c.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:c.first_name||c.last_name?`${c.first_name} ${c.last_name}`:"Player"})]},c.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:g(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:g(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:g((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_status)==h.PENDING&&s.jsx("div",{className:"mt-3 flex items-center justify-between",children:s.jsx("button",{onClick:()=>{i(`/user/reservation-payment/${e==null?void 0:e.reservation_id}?type=${e==null?void 0:e.booking_type}`)},disabled:p==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${p==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:p==="0min"?"Time Expired":"Pay now"})}),(e==null?void 0:e.booking_status)==h.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{i(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}function Ys({reservation:e,club:N,clubSports:a,players:t,coach:i}){var c;const p=$(),j=R(e==null?void 0:e.reservation_updated_at);return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{children:(e==null?void 0:e.booking_status)===h.PENDING&&s.jsx(B,{})||(e==null?void 0:e.booking_status)===h.SUCCESS&&s.jsx(I,{})||(e==null?void 0:e.booking_status)===h.FAIL&&s.jsx(O,{})}),s.jsx("div",{children:s.jsx(Y,{timeLeft:j})})]})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(c=a==null?void 0:a.find(n=>n.id===(e==null?void 0:e.sport_id)))==null?void 0:c.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:D(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[k(e==null?void 0:e.start_time)," -"," ",k(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"COACH"})}),s.jsx("div",{className:"flex flex-col gap-2",children:i&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:i.photo||"/default-avatar.png",alt:i.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:i.first_name||i.last_name?`${i.first_name} ${i.last_name}`:"Coach"})]},i.user_id)})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t.map(n=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:n.photo||"/default-avatar.png",alt:n.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:n.first_name||n.last_name?`${n.first_name} ${n.last_name}`:"Player"})]},n.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:g(N==null?void 0:N.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:"$12.50"})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:g((e==null?void 0:e.club_fee)+12.5)})]})]}),s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{p(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}function Is({reservation:e,club:N,clubSports:a,players:t}){var j;const i=R(e==null?void 0:e.reservation_updated_at),p=$();return s.jsx("div",{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("div",{children:(e==null?void 0:e.booking_status)===h.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(B,{}),s.jsx(Y,{timeLeft:i})]})||(e==null?void 0:e.booking_status)===h.SUCCESS&&s.jsx(I,{})||(e==null?void 0:e.booking_status)===h.FAIL&&s.jsx(O,{})})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(j=a==null?void 0:a.find(c=>c.id===(e==null?void 0:e.sport_id)))==null?void 0:j.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DATE"})}),s.jsx("p",{className:"mt-1 font-medium",children:D(e==null?void 0:e.booking_date).format("MMM D, YYYY")})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"TIME"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[k(e==null?void 0:e.start_time)," -"," ",k(e==null?void 0:e.end_time)]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"})}),s.jsx("div",{className:"flex flex-col gap-2",children:t==null?void 0:t.map(c=>s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("img",{src:c.photo||"/default-avatar.png",alt:c.first_name,className:"h-8 w-8 rounded-full"}),s.jsx("p",{className:"font-medium capitalize",children:c.first_name||c.last_name?`${c.first_name} ${c.last_name}`:"Player"})]},c.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:g(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:g(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:g((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{p(`/user/payment-receipt/${e==null?void 0:e.reservation_id}?type=clinic`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})})}const Os=new U;function Fs({reservation:e,club:N,clubSports:a,players:t}){var w;const i=$(),p=R(e==null?void 0:e.reservation_updated_at),{dispatch:j}=u.useContext(q),[c,n]=u.useState(!1),y=async m=>{if(!m){V(j,"Email is required",5e3,"error");return}try{n(!0);const b=await Os.callRawAPI(`/v3/api/custom/courtmatchup/user/buddy/${e==null?void 0:e.buddy_id}/send-mail?email=${m}`,{},"GET");console.log(b),V(j,"Email reminder sent",5e3,"success")}catch(b){console.log(b),V(j,b==null?void 0:b.message,5e3,"error")}finally{n(!1)}};return s.jsxs("div",{children:[c&&s.jsx(cs,{}),s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:" px-5 pt-5",children:s.jsxs("div",{className:"flex w-full items-center justify-between gap-2 rounded-xl bg-gray-100 p-2 px-3",children:[s.jsx("div",{children:s.jsx("div",{className:"text-sm text-gray-500",children:"STATUS"})}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsxs("div",{children:[(e==null?void 0:e.num_needed)==(e==null?void 0:e.num_players)&&s.jsx(as,{title:"Group full"}),(e==null?void 0:e.num_needed)!=(e==null?void 0:e.num_players)&&s.jsx(is,{numberOfBuddies:e==null?void 0:e.num_needed})]})})]})}),s.jsxs("div",{className:"divide-y px-5 py-1",children:[s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"REQUEST MADE"})}),D(e==null?void 0:e.reservation_created_at).format("MMM D, YYYY")]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"DTE & TIME"})}),D(e==null?void 0:e.booking_date).format("MMM D, YYYY")," •"," ",k(e==null?void 0:e.start_time)," -"," ",k(e==null?void 0:e.end_time)]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"})}),(w=a==null?void 0:a.find(m=>m.id===(e==null?void 0:e.sport_id)))==null?void 0:w.name," ",(e==null?void 0:e.type)&&`• ${e==null?void 0:e.type}`," ",(e==null?void 0:e.sub_type)&&`• ${e==null?void 0:e.sub_type}`]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"NOTES"})}),s.jsxs("p",{className:"mt-1 font-medium",children:[e==null?void 0:e.ntrp," ",(e==null?void 0:e.max_ntrp)&&`- ${e==null?void 0:e.max_ntrp}`]})]}),s.jsxs("div",{className:"py-3",children:[s.jsx("div",{className:" py-1 ",children:s.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"})}),s.jsx("p",{className:"mt-1 font-medium",children:`${e==null?void 0:e.num_needed}/${e==null?void 0:e.num_players}`})]}),s.jsxs("div",{className:"py-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"PLAYERS"}),s.jsx("button",{className:"rounded-lg border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700",children:"Email all"})]}),s.jsx("div",{className:"mt-4 flex flex-col gap-4",children:t.map(m=>s.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-gray-100 p-2",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:m.photo||"/default-avatar.png",alt:m.first_name,className:"h-10 w-10 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("p",{className:"font-medium capitalize",children:m.first_name||m.last_name?`${m.first_name} ${m.last_name}`:"Player"}),s.jsx("p",{className:"text-sm text-gray-500",children:m.email})]})]}),s.jsxs("div",{className:"flex flex-col items-end gap-3",children:[s.jsx("button",{onClick:()=>y(m.email),className:"rounded-lg bg-white px-2 py-1 text-sm text-gray-700",children:"Send email"}),s.jsxs("p",{className:"text-sm text-gray-700",children:["NTRP: ",m.ntrp]})]})]},m.user_id))})]})]}),s.jsx("div",{className:"px-5 py-3",children:s.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),s.jsxs("div",{className:"mt-2",children:[s.jsxs("div",{className:"mb-2 flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Club fee"}),s.jsx("p",{className:"text-sm ",children:g(e==null?void 0:e.club_fee)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("p",{className:"text-sm ",children:"Service fee"}),s.jsx("p",{className:"text-sm ",children:g(e==null?void 0:e.service_fee)})]}),s.jsx("div",{className:"my-2 border-t border-gray-300"}),s.jsxs("div",{className:"flex justify-between font-medium",children:[s.jsx("p",{children:"Total"}),s.jsx("p",{children:g((e==null?void 0:e.club_fee)+(e==null?void 0:e.service_fee))})]})]}),(e==null?void 0:e.booking_status)==h.PENDING&&s.jsx("div",{className:"mt-3 flex items-center justify-between",children:s.jsx("button",{onClick:()=>{i(`/user/reservation-payment/${e==null?void 0:e.reservation_id}?type=${e==null?void 0:e.booking_type}`)},disabled:p==="0min",className:`w-full rounded-lg border border-gray-300 px-2 py-2 text-sm text-white ${p==="0min"?"cursor-not-allowed bg-gray-400":"bg-primaryBlue"}`,children:p==="0min"?"Time Expired":"Pay now"})}),(e==null?void 0:e.booking_status)==h.SUCCESS&&s.jsxs("div",{className:"mt-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.75 9.75102V18.2471C2.75 18.7994 3.19772 19.2471 3.75 19.2471L20.2461 19.2471C20.7984 19.2471 21.2461 18.7994 21.2461 18.2471V9.75102M2.75 9.75102V5.75391C2.75 5.20162 3.19772 4.75391 3.75 4.75391H20.2451C20.7962 4.75391 21.2435 5.19985 21.2444 5.751C21.2467 7.08434 21.2461 8.41768 21.2461 9.75102M2.75 9.75102H21.2461M6.75 13.251H9.75",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),s.jsx("p",{className:"text-sm font-medium",children:"Credit card • 0089"})]}),s.jsx("button",{onClick:()=>{i(`/user/payment-receipt/${e==null?void 0:e.reservation_id}`)},className:"rounded-md border border-gray-300 px-2 py-1 text-sm text-gray-500",children:"View receipt"})]}),s.jsx("div",{className:"mt-3",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Beatae id illum labore excepturi veritatis facere ducimus architecto quasi incidunt earum eligendi exercitationem, ipsam voluptas amet quaerat? Alias inventore eum debitis!"})})]})})]})]})}let es=new U;function Hs({isOpen:e,onClose:N,clubSports:a,reservation:t,club:i}){const{dispatch:p}=u.useContext(q),{dispatch:j}=u.useContext(ds),[c,n]=u.useState(!1),[y,w]=u.useState([]),[m,b]=u.useState(null);async function F(){try{const _=await ks(p,j,"user",JSON.parse(t==null?void 0:t.player_ids),"user|user_id");w(_.list)}catch(_){console.error(_)}}async function z(){try{es.setTable("user");const _=await es.callRestAPI({id:t==null?void 0:t.coach_id},"GET");b(_.model)}catch(_){console.error(_)}}return u.useEffect(()=>{(async()=>(n(!0),await F(),await z(),n(!1)))()},[t]),t?c?s.jsx(cs,{}):(console.log({reservation:t,sports:a,players:y,coach:m}),s.jsxs(Ss,{isOpen:e,onClose:N,title:t.booking_type==="Court"?"Court details":t.booking_type==="Coach"?"Coach details":t.booking_type==="Clinic"?"Clinic details":"Details",showFooter:!1,className:"!p-0",children:[t.booking_type==="Court"&&s.jsx(Bs,{reservation:t,club:i,clubSports:a,players:y}),t.booking_type==="Find Buddy"&&s.jsx(Fs,{reservation:t,club:i,clubSports:a,players:y}),t.booking_type==="Coach"&&s.jsx(Ys,{reservation:t,club:i,clubSports:a,players:y,coach:m}),t.booking_type==="Clinic"&&s.jsx(Is,{reservation:t,club:i,clubSports:a,players:y})]})):null}let ls=new U,Gs=new Es;const ts=[{header:"Date & Time",accessor:"date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Booking Type",accessor:"booking_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Players",accessor:"players",isSorted:!1,isSortedDesc:!1,mappingExist:!1},{header:"Price",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"reservation_status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{1:"Active",0:"Inactive"}}],$e=()=>{const{dispatch:e,state:N}=d.useContext(q),{dispatch:a}=d.useContext(ds),[t,i]=d.useState([]),[p,j]=d.useState(10),[c,n]=d.useState(0);d.useState(0);const[y,w]=d.useState(0),[m,b]=d.useState(!1),[F,z]=d.useState(!1),[_,ms]=d.useState(!1);d.useState(!1),d.useState([]),d.useState([]),d.useState("eq");const[H,G]=d.useState(!0),[xs,Vs]=d.useState(!1),[ps,Us]=d.useState(!1);d.useState(),$();const Z=d.useRef(null),[qs,ns]=d.useState(!1),[K,J]=d.useState(null);d.useState([]),u.useState(!1);const[us,hs]=u.useState([]);u.useState([]),u.useState([]);const[P,js]=u.useState("upcoming");u.useState(!1),u.useState(!1);const[fs,gs]=u.useState(null),Ns=[{id:"upcoming",label:"Upcoming",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.05153 8.29769L3.13684 14.4527C3.21675 14.906 3.64897 15.2086 4.10222 15.1287L8.42993 14.3656M2.05153 8.29769L1.61741 5.83567C1.53749 5.38242 1.84013 4.95021 2.29338 4.87029L11.7311 3.20616C12.1844 3.12624 12.6166 3.42888 12.6965 3.88213L13.1306 6.34414L2.05153 8.29769ZM13.3333 9.79243V11.6674L15 13.3341M18.5417 11.6674C18.5417 14.5439 16.2098 16.8758 13.3333 16.8758C10.4569 16.8758 8.125 14.5439 8.125 11.6674C8.125 8.79095 10.4569 6.4591 13.3333 6.4591C16.2098 6.4591 18.5417 8.79095 18.5417 11.6674Z",stroke:"black","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})},{id:"past",label:"Past",icon:()=>s.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("rect",{width:"20",height:"20",fill:"white"}),s.jsx("path",{d:"M12.5 7.91602L8.75001 12.4993L7.08334 10.8327M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.74281 17.7077 2.29167 14.2565 2.29167 9.99935C2.29167 5.74215 5.74281 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})},{id:"cancelled",label:"Cancelled",icon:()=>s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M12.5 7.49935L7.5 12.4993M12.5 12.4993L7.5 7.49935M17.7083 9.99935C17.7083 14.2565 14.2572 17.7077 10 17.7077C5.7428 17.7077 2.29166 14.2565 2.29166 9.99935C2.29166 5.74215 5.7428 2.29102 10 2.29102C14.2572 2.29102 17.7083 5.74215 17.7083 9.99935Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})}],ys=Ps({id:A(),email:A(),role:A(),status:A()});Ls({resolver:Ds(ys)});function bs(){M()}function _s(){M()}async function M(l,o,L={},f=[]){G(!(ps||xs));try{const x=await ls.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{},"GET");if(x){G(!1);const T=new Date;T.setHours(0,0,0,0);let E=x.list;P==="upcoming"?E=x.list.filter(C=>{const S=new Date(C.booking_date);return S.setHours(0,0,0,0),S>=T}):P==="past"?E=x.list.filter(C=>{const S=new Date(C.booking_date);return S.setHours(0,0,0,0),S<T}):P==="cancelled"&&(E=x.list.filter(C=>C.booking_status===2)),i(E)}}catch(x){G(!1),console.log("ERROR",x),ws(a,x.message)}}const os=async()=>{try{const l=localStorage.getItem("user"),o=await Gs.getOne("user",l,{}),L=await ls.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${o.model.club_id}`,{},"GET");hs(L.sports),gs(L.model)}catch(l){console.error(l)}};d.useEffect(()=>{e({type:"SETPATH",payload:{path:"my-reservations"}});const o=setTimeout(async()=>{await M(1,p,{}),await os()},700);return()=>{clearTimeout(o)}},[]),d.useEffect(()=>{M(1,p,{})},[P]);const Q=l=>{Z.current&&!Z.current.contains(l.target)&&ms(!1)};d.useEffect(()=>(document.addEventListener("mousedown",Q),()=>{document.removeEventListener("mousedown",Q)}),[]);const X=l=>{J(l),ns(!0)};return console.log("reservation data",t),s.jsxs("div",{children:[s.jsxs("div",{className:"bg-white px-4 pt-4",children:[s.jsx("h1",{className:"mb-6 text-2xl font-semibold",children:"My Reservations"}),s.jsx("div",{className:"mb-0 flex max-w-fit  text-sm",children:Ns.map(l=>s.jsxs("button",{onClick:()=>js(l.id),className:`flex items-center gap-2 bg-transparent px-3 py-3 ${P===l.id?"border-b-2 border-primaryBlue":""}`,children:[l.icon(),s.jsx("span",{className:"",children:l.label})]},l.id))})]}),s.jsxs("div",{className:"h-screen px-8",children:[H&&s.jsx(As,{}),H?s.jsx(Rs,{}):s.jsxs("div",{className:"overflow-x-auto",children:[s.jsxs("table",{className:"w-full min-w-[1024px] ",children:[s.jsx("thead",{children:s.jsx("tr",{children:ts.map((l,o)=>s.jsx("th",{scope:"col",className:"px-6 py-4 text-left text-sm font-medium text-gray-500",children:l.header},o))})}),s.jsx("tbody",{className:"divide-y-8 divide-gray-50",children:t.map((l,o)=>{const L=R(l==null?void 0:l.reservation_updated_at);return s.jsx("tr",{onClick:()=>X(l),className:"hover:bg-gray-40 cursor-pointer rounded-lg bg-white px-4 py-3 text-gray-500",children:ts.map((f,x)=>{var T,E,C,S,W,r,v;return f.accessor==""?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:()=>X(l),children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})})},x):f.accessor==="reservation_status"?s.jsxs("td",{className:"flex gap-2 whitespace-nowrap px-6 py-5 text-sm",children:[(l==null?void 0:l.booking_type)=="Find Buddy"&&s.jsxs(s.Fragment,{children:[(l==null?void 0:l.num_needed)==(l==null?void 0:l.num_players)&&s.jsx(as,{title:"Group full"}),(l==null?void 0:l.num_needed)!=(l==null?void 0:l.num_players)&&s.jsx(is,{numberOfBuddies:l==null?void 0:l.num_needed})]}),(l==null?void 0:l.booking_status)===h.PENDING&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(B,{}),s.jsx(Y,{timeLeft:L})]})||(l==null?void 0:l.booking_status)===h.SUCCESS&&s.jsx(I,{})||(l==null?void 0:l.booking_status)===h.FAIL&&s.jsx(O,{})]},x):f.mappingExist?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:f.mappings[l[f.accessor]]},x):f.accessor==="type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(T=Ms.find(Cs=>Cs.value===(l==null?void 0:l.type)))==null?void 0:T.label},x):f.accessor==="date"?s.jsxs("td",{className:"whitespace-nowrap rounded-l-3xl px-6 py-4",children:[Ts((l==null?void 0:l.booking_date)||"")||"--"," "," | "," ",ss((l==null?void 0:l.start_time)||"")||"--"," "," - "," ",ss((l==null?void 0:l.end_time)||"")||"--"]},x):f.accessor==="booking_type"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:(l==null?void 0:l.booking_type)||"--"},x):f.accessor==="players"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(E=l==null?void 0:l.booking)!=null&&E.player_ids?`${JSON.parse((C=l==null?void 0:l.booking)==null?void 0:C.player_ids).length} players`:"0 players"},x):f.accessor==="price"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:g((l==null?void 0:l.price)||0)},x):f.accessor==="user"?s.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:!((S=l==null?void 0:l.user)!=null&&S.first_name)||!((W=l==null?void 0:l.user)!=null&&W.last_name)?"--":`${(r=l==null?void 0:l.user)==null?void 0:r.first_name} ${(v=l==null?void 0:l.user)==null?void 0:v.last_name}`},x):s.jsx("td",{className:"whitespace-nowrap px-6 py-4 capitalize",children:l[f.accessor]},x)})},o)})})]}),!H&&t.length===0&&s.jsx("div",{className:"w-full px-6 py-4 text-center",children:s.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),s.jsx($s,{currentPage:y,pageCount:c,pageSize:p,canPreviousPage:m,canNextPage:F,updatePageSize:l=>{j(l),M()},previousPage:bs,nextPage:_s,gotoPage:l=>M()}),s.jsx(Hs,{isOpen:!!K,onClose:()=>J(null),reservation:K,clubSports:us,club:fs})]})," "]})};export{$e as default};
