import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as s,u as ve,j as je,f as fe,b as Ne,L as V}from"./vendor-851db8c1.js";import"./BottomDrawer-f3121b82.js";import{M as we,T as _e,u as Se,G as Ce,A as be,aI as ke,o as Te,an as Be,h as Re,x as p,d as Fe,aE as Ee,as as Le,D as Pe,H as De}from"./index-97f6f167.js";import{B as Ie}from"./BackButton-11ba52b2.js";import"./TimeSlots-9ce7c837.js";import{A as Oe}from"./AddPlayers-4c2a1b90.js";import{a as G}from"./ReservationSummary-c649f1d8.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";let f=new we,$=new _e;const Bt=({isOpen:Ae=!0,onClose:Ye=()=>{},surfaces:He=[]})=>{var F,E,L,P,D,I,O,A,Y,H;const[w,Z]=s.useState(1);s.useState(null),s.useState(null),s.useState(null),s.useState(null);const[q,J]=s.useState([]),[U,W]=s.useState([]);s.useState(!1),s.useState({from:null,until:null});const[_,Ve]=s.useState("players"),[o,S]=s.useState([]),[C,K]=s.useState(!1),[z,Q]=s.useState(1),[X,b]=s.useState(!1);s.useState(null);const[M,Ge]=s.useState(null),[ee,te]=s.useState(null),[k,se]=s.useState(null),[ae,ne]=s.useState(null),[ie,re]=s.useState(null),[oe,T]=s.useState(!1),l=ve(),{id:N}=je(),{club:i,sports:B,user_subscription:d,user_permissions:x,club_membership:v}=Se(),[t,le]=s.useState(null);s.useContext(Ce),s.useContext(be);const ce=fe(),n=s.useMemo(()=>!(d!=null&&d.planId)||!(v!=null&&v.length)?null:v.find(a=>a.plan_id===d.planId),[d,v]),de=s.useMemo(()=>{var r,j;if(((r=n==null?void 0:n.advance_booking_enabled)==null?void 0:r.clinic)===!1){const y=new Date;return y.setFullYear(y.getFullYear()+10),y}const a=((j=n==null?void 0:n.advance_booking_days)==null?void 0:j.clinic)||10,m=new Date,g=new Date;return g.setDate(m.getDate()+a),g},[n]),me=localStorage.getItem("user"),[u,h]=s.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),pe=async()=>{try{const a=await $.getOne("clinics",N,{});le(a.model)}catch(a){console.error(a)}},ue=async()=>{try{const a=await $.getList("user",{filter:["role,cs,user",`club_id,cs,${i==null?void 0:i.id}`]});J(a.list)}catch(a){console.error(a)}},xe=async()=>{try{const a=await f.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");W(a.groups)}catch(a){console.error(a)}};s.useEffect(()=>{(async()=>(b(!0),await pe(),await ue(),await xe(),b(!1)))()},[]),s.useEffect(()=>{},[_]);const he=a=>{S(m=>m.some(r=>r.id===a.id)?m.filter(r=>r.id!==a.id):[...m,a])},c=ke({costPerHead:t==null?void 0:t.cost_per_head,playerCount:o==null?void 0:o.length,feeSettings:i==null?void 0:i.fee_settings}),ge=async()=>{var m,g;if(!(d!=null&&d.planId)){h({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to join clinics",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(x!=null&&x.allowClinic)){h({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${x==null?void 0:x.planName}) does not include clinic bookings. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(new Date(t==null?void 0:t.date)>de&&((m=n==null?void 0:n.advance_booking_enabled)==null?void 0:m.clinic)!==!1){const r=((g=n==null?void 0:n.advance_booking_days)==null?void 0:g.clinic)||10;h({isOpen:!0,title:"Date Selection Error",message:`Your membership plan only allows booking clinics ${r} days in advance. Please select a clinic within your allowed date range.`,type:"warning"});return}if(!o.length){h({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}T(!0);try{const r=await f.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/createe",{amount:c.total},"POST"),j={sport_id:t==null?void 0:t.sport_id,type:t==null?void 0:t.type,sub_type:t==null?void 0:t.sub_type,date:t==null?void 0:t.date,start_time:t==null?void 0:t.start_time,end_time:t==null?void 0:t.end_time,duration:1,price:c.total,clinic_fee:c.clinicFee,service_fee:c.serviceFee,player_ids:o.map(ye=>ye.id),buddy_details:null,payment_status:0,payment_intent:r.payment_intent,reservation_type:Le.clinic,clinic_id:N},y=await f.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",j,"POST");f.setTable("activity_logs");const $e=await f.callRestAPI({activity_type:Pe.clinic,user_id:me,club_id:i==null?void 0:i.id,action_type:De.CREATE,data:JSON.stringify(j),description:"Created a clinic reservation"},"POST");ne(r.client_secret),re(r.payment_intent),se(y.reservation_id),te(y.booking_id),Z(2)}catch(r){console.error(r),h({isOpen:!0,title:"Reservation Error",message:r.message||"Error creating clinic reservation",type:"error"})}finally{T(!1)}};Ne.useEffect(()=>{Te({path:`/user/clinic-booking/${N}`,clubName:i==null?void 0:i.name,favicon:i==null?void 0:i.club_logo,description:`Join ${t==null?void 0:t.name}`})},[i==null?void 0:i.club_logo]);const R=i!=null&&i.clinic_description?JSON.parse(i==null?void 0:i.clinic_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"h-full",children:[e.jsx(Be,{isOpen:u.isOpen,onClose:()=>h({...u,isOpen:!1}),title:u.title,message:u.message,actionButtonText:u.actionButtonText,actionButtonLink:u.actionButtonLink,type:u.type}),X&&e.jsx(Re,{}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"flex items-center justify-center bg-white p-4",children:_==="players"&&e.jsx("div",{className:" ",children:"Clinic Booking"})}),e.jsxs("div",{className:"p-4",children:[e.jsx(Ie,{onBack:()=>{ce(-1)}}),w===1&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-4",children:[((F=n==null?void 0:n.advance_booking_enabled)==null?void 0:F.clinic)===!1?e.jsx("div",{className:"rounded-lg bg-blue-50 p-3 text-sm text-blue-700",children:"You can sign up for a clinic for any future date."}):((E=n==null?void 0:n.advance_booking_days)==null?void 0:E.clinic)!==void 0&&e.jsxs("div",{className:"rounded-lg bg-blue-50 p-3 text-sm text-blue-700",children:["You can sign up for a clinic up to"," ",(L=n==null?void 0:n.advance_booking_days)==null?void 0:L.clinic," ",((P=n==null?void 0:n.advance_booking_days)==null?void 0:P.clinic)===1?"day":"days"," ","in advance."]}),e.jsx(G,{selectedSport:t==null?void 0:t.sport_id,sports:B,selectedType:t==null?void 0:t.type,selectedSubType:t==null?void 0:t.sub_type,selectedDate:t==null?void 0:t.date,selectedTimes:t==null?void 0:t.start_time,clinic:{...t,...(D=l==null?void 0:l.state)==null?void 0:D.clinic}})]}),e.jsx(Oe,{players:q,groups:U,selectedPlayers:o,onPlayerToggle:he,isFindBuddyEnabled:C,onFindBuddyToggle:()=>K(!C),playersNeeded:z,onPlayersNeededChange:Q,showPlayersNeeded:!1,showCurrentGroup:!1,setSelectedPlayers:S,maximumPlayers:((O=(I=l==null?void 0:l.state)==null?void 0:I.clinic)==null?void 0:O.slots_remaining)&&parseInt((Y=(A=l==null?void 0:l.state)==null?void 0:A.clinic)==null?void 0:Y.slots_remaining)}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",o.length,")"]}),e.jsx("div",{className:"mt-1",children:o.map(a=>e.jsxs("div",{className:"text-sm",children:[a.first_name," ",a.last_name]},a.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Clinic Fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",p(t==null?void 0:t.cost_per_head)," ×"," ",o.length," players)"]})]}),e.jsx("span",{children:p(c.clinicFee)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:p(c.serviceFee)})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:p(c.total)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Fe,{loading:oe,onClick:ge,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:R.reservation_description}),e.jsxs("div",{className:"space-y-2 text-center text-sm text-gray-500",children:[e.jsx("p",{children:"(You will not be charged yet)"}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})})]})]}),w===2&&e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(G,{selectedSport:t==null?void 0:t.sport_id,sports:B,selectedType:t==null?void 0:t.type,selectedSubType:t==null?void 0:t.sub_type,selectedDate:t==null?void 0:t.date,selectedTimes:t==null?void 0:t.start_time,clinic:{...t,...(H=l==null?void 0:l.state)==null?void 0:H.clinic}})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",o.length,")"]}),e.jsx("div",{className:"mt-1",children:o.map(a=>e.jsxs("div",{className:"text-sm",children:[a.first_name," ",a.last_name]},a.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Clinic Fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",p(t==null?void 0:t.cost_per_head)," ×"," ",o.length," players)"]})]}),e.jsx("span",{children:p(c.clinicFee)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:p(c.serviceFee)})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:p(c.total)})]}),e.jsxs("div",{children:[e.jsx(Ee,{user:M,bookingId:ee,reservationId:k,clientSecret:ae,paymentIntent:ie,navigateRoute:`/user/payment-success/${k}?type=clinic`}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:R.payment_description})]}),e.jsxs("div",{className:"space-y-4 text-sm text-gray-500",children:[e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(V,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(V,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})]})})]})]})]})]})]})};export{Bt as default};
