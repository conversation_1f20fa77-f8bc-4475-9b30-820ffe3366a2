import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as K,r as o}from"./vendor-851db8c1.js";import{R as G,a3 as U,aF as z,G as ee,A as te,h as se,T as ae,M as ne,b as le,t as re}from"./index-97f6f167.js";import"./FindByTimeTab-6ce8621d.js";import{p as ie}from"./index.esm-4466ae65.js";import{f as Q,s as oe,n as ce}from"./date-fns-07266b7d.js";import{C as de}from"./Calendar-282b3fcf.js";import{h as me}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index.esm-b72032a7.js";import"./BottomDrawer-f3121b82.js";import"./AddPlayers-4c2a1b90.js";import"./react-tooltip-7a26650a.js";import"./BackButton-11ba52b2.js";import"./ReservationSummary-c649f1d8.js";import"./TimeSlots-9ce7c837.js";import"./SportTypeSelection-cc97819b.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./CalendarIcon-b3488133.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";function J({isOpen:n,onClose:b,clinic:p}){const x=K(),[j,C]=o.useState(!1),h=()=>{C(!j)};return p?e.jsx(G,{isOpen:n,onClose:b,title:p.name,showFooter:!1,primaryButtonText:"Join",showOnlyPrimary:!0,className:"!p-0",children:e.jsxs("div",{className:"flex h-full flex-col pb-4",children:[e.jsxs("div",{className:"flex flex-1 flex-col gap-4 p-5 pb-6",children:[e.jsx("div",{className:"inline-flex items-center gap-2",children:p.slots_remaining>0?e.jsxs("span",{className:"rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-sm text-[#176448]",children:["Slots available: ",p.slots_remaining," (out of"," ",p.max_participants,")"]}):e.jsx("span",{className:"rounded-full border border-red-800 bg-red-50 px-3 py-1 text-sm text-red-800",children:"No slots available"})}),e.jsxs("div",{className:"border-1 space-y-1 rounded-xl border border-gray-200 bg-gray-100 p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DATE & TIME"}),e.jsxs("p",{className:"text-base",children:[new Date(p.clinic_date).toLocaleDateString("en-US",{month:"long",day:"numeric"})," ","• ",U(p.clinic_start_time)," -"," ",U(p.clinic_end_time)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DETAILS"}),e.jsx("p",{className:"text-base",children:p.details})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"COACHES"}),e.jsx("div",{className:"space-y-2",children:p.coach_details.map(w=>e.jsxs("p",{className:"text-base",children:[w.first_name," ",w.last_name]},w.user_id))})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"SPORT & TYPE"}),e.jsxs("p",{className:"text-base",children:["Tennis • Indoors •"," ",p.surface_id===1?"Hard Court":"Clay Court"]})]}),e.jsx("div",{className:"border border-b"}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"NTRP"}),e.jsx("p",{className:"text-base",children:"4.0-5.0"})]})]}),e.jsx("div",{className:"sticky bottom-0 border-t border-gray-200 bg-gray-100 p-4",children:p.slots_remaining>0?e.jsxs("button",{onClick:()=>{x(`/user/clinic-booking/${p.id}`,{state:{clinic:p}})},className:"flex w-full items-center justify-center gap-2 rounded-xl bg-primaryBlue py-3 text-center text-white hover:bg-blue-900",children:[e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10.75 11.5H9.25C8.0197 11.4995 6.81267 11.8354 5.75941 12.4712C4.70614 13.107 3.8467 14.0186 3.274 15.1075C3.2579 14.9054 3.2499 14.7027 3.25 14.5C3.25 10.3578 6.60775 7 10.75 7V2.875L18.625 9.25L10.75 15.625V11.5ZM9.25 10H12.25V12.481L16.2408 9.25L12.25 6.019V8.5H10.75C9.88769 8.49903 9.03535 8.68436 8.25129 9.04332C7.46724 9.40227 6.76999 9.92637 6.20725 10.5797C7.17574 10.1959 8.20822 9.99919 9.25 10Z",fill:"white"})})}),e.jsx("span",{children:" Join"})]}):e.jsx("div",{className:"rounded-2xl border border-gray-200 bg-white p-4 shadow-sm",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Get notifed"}),e.jsx("div",{className:"flex items-center",children:e.jsx("button",{type:"button",className:`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${j?"bg-blue-600":"bg-gray-200"}`,role:"switch","aria-checked":j,onClick:h,children:e.jsx("span",{"aria-hidden":"true",className:`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${j?"translate-x-5":"translate-x-0"}`})})})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"We will email you when slots for this clinic become available again, e.g. if someone opts-put."})]})})})})]})}):null}function q({getActiveFiltersCount:n,clearFilters:b,setIsFilterModalOpen:p,availableOnly:x,toggleAvailableSlots:j,sortOrder:C,showSortOptions:h,setShowSortOptions:w,sortClinics:$}){return e.jsxs("div",{className:"mb-4 flex flex-col justify-between gap-3 sm:mb-6 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex items-center gap-3 sm:gap-4",children:[e.jsxs("button",{onClick:()=>p(!0),className:"flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-1.5 sm:px-4 sm:py-2",children:[e.jsx(ie,{className:"text-blue-600"}),e.jsx("span",{className:"text-sm text-gray-700 sm:text-base",children:"Filter"}),n()>0&&e.jsx("span",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs text-white",children:n()})]}),n()>0&&e.jsx("button",{onClick:b,className:"text-sm text-gray-500 hover:underline sm:text-base",children:"Clear all"})]}),e.jsxs("div",{className:"mt-3 flex flex-wrap items-center gap-3 sm:mt-0 sm:gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"whitespace-nowrap text-xs text-gray-700 sm:text-sm",children:"Available slot only"}),e.jsx("button",{onClick:j,className:`relative h-5 w-10 rounded-full transition-colors duration-200 ease-in-out sm:h-6 sm:w-12 ${x?"bg-blue-600":"bg-gray-200"}`,children:e.jsx("div",{className:`absolute top-0.5 h-4 w-4 transform rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out sm:h-5 sm:w-5 ${x?"translate-x-5 sm:translate-x-6":"translate-x-0.5 sm:translate-x-1"}`})})]}),e.jsxs("div",{className:"relative border-gray-200 sm:border-l sm:pl-4",children:[e.jsxs("button",{onClick:()=>w(!h),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1.5 text-xs sm:gap-2 sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",C==="desc"?"Latest":"Earliest",")"]}),e.jsx(z,{size:16,className:`text-gray-400 transition-transform duration-200 ${h?"rotate-180":""}`})]}),h&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>$("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${C==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",C==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>$("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${C==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",C==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]})}function V({clinic:n}){return e.jsx("div",{className:"cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4",children:e.jsxs("div",{className:"flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsx("h3",{className:"text-base font-medium sm:text-lg",children:n.name}),n.type==1&&e.jsx("span",{className:"rounded bg-blue-600 px-2 py-1 text-xs text-white",children:"REGISTERED"})]}),e.jsxs("p",{className:"mt-1 text-xs text-gray-600 sm:text-sm",children:[new Date(n.clinic_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})," - "," ",new Date(n.end_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})," • ",U(n.clinic_start_time)," -"," ",U(n.clinic_end_time),n.end_date&&e.jsxs("span",{children:[" ","-"," ",new Date(n.end_date).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]})]})]}),e.jsx("div",{className:"flex flex-col gap-2 sm:items-end sm:gap-4",children:n.slots_remaining>0?e.jsxs("span",{className:"w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]",children:["Slots available: ",n.slots_remaining," (out of"," ",n.max_participants,")"]}):e.jsx("span",{className:"w-fit rounded-full border border-red-800 bg-red-50 px-3 py-1 text-xs text-red-800",children:"No slots available"})})]})})}function xe({programs:n,fetchClinics:b,FiltersContent:p,filters:x,setFilters:j,clearFilters:C}){const[h,w]=o.useState(!1),[$,O]=o.useState(!1),[L,t]=o.useState(null),[m,c]=o.useState(n),[D,N]=o.useState(!1);o.useState(!1);const[k,A]=o.useState("desc"),[S,M]=o.useState(!1),[_,W]=o.useState(new Date),R=async r=>{N(!0);const g=Q(oe(r,{weekStartsOn:0}),"yyyy-MM-dd"),y=Q(ce(r,{weekStartsOn:0}),"yyyy-MM-dd");let f=[];f.push(`start_date=${g}`),f.push(`end_date=${y}`);const l=Object.entries(x.days).filter(([u,a])=>a).map(([u])=>u.toLowerCase());l.length>0&&f.push(`weekday=${l.join(",")}`);const i=Object.entries(x.timeOfDay).filter(([u,a])=>a).map(([u])=>u.toLowerCase());i.length>0&&f.push(`times=${i.join(",")}`);const s=f.join("&");await b(s),N(!1)},B=r=>{const g=[...m].sort((y,f)=>{const l=new Date(y.clinic_date+" "+y.clinic_start_time),i=new Date(f.clinic_date+" "+f.clinic_start_time);return r==="asc"?l-i:i-l});c(g),A(r),M(!1)};o.useEffect(()=>{let r=[...n];h&&(r=r.filter(y=>parseInt(y.slots_remaining)>0));const g=r.sort((y,f)=>{const l=new Date(y.clinic_date+" "+y.clinic_start_time),i=new Date(f.clinic_date+" "+f.clinic_start_time);return k==="asc"?l-i:i-l});c(g)},[n,k,h]),o.useEffect(()=>{R(_)},[]);const T=()=>{const r=Object.values(x.days).filter(Boolean).length,g=Object.values(x.timeOfDay).filter(Boolean).length,y=x.price.from||x.price.to?1:0;return r+g+y},E=async()=>{N(!0);let r=[];const g=Object.entries(x.days).filter(([l,i])=>i).map(([l])=>l.toLowerCase());g.length>0&&r.push(`weekday=${g.join(",")}`);const y=Object.entries(x.timeOfDay).filter(([l,i])=>i).map(([l])=>l.toLowerCase());y.length>0&&r.push(`times=${y.join(",")}`);const f=r.join("&");await b(f),O(!1),N(!1)},P=()=>{if(w(!h),h)c(n);else{const r=n.filter(g=>parseInt(g.slots_remaining)>0);c(r)}};return console.log("clinics",m),e.jsxs("div",{className:"mx-auto mt-3 max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:mt-5 sm:p-4",children:[e.jsx(q,{getActiveFiltersCount:T,clearFilters:C,setIsFilterModalOpen:O,availableOnly:h,toggleAvailableSlots:P,sortOrder:k,showSortOptions:S,setShowSortOptions:M,sortClinics:B}),e.jsx("div",{className:"space-y-3 sm:space-y-4",children:m.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:h?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(T()>0||h)&&e.jsx("button",{onClick:()=>{C(),w(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):m.map(r=>e.jsx("div",{onClick:()=>t(r),children:e.jsx(V,{clinic:r})},r.id))}),e.jsx(G,{isOpen:$,onClose:()=>O(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:E,className:"bg-gray-100",submitting:D,children:e.jsx(p,{filters:x,setFilters:j})}),e.jsx(J,{isOpen:L!==null,onClose:()=>t(null),clinic:L})]})}function ue({programs:n,fetchClinics:b,FiltersContent:p,filters:x,setFilters:j,clearFilters:C,clubProfile:h}){const[w,$]=o.useState(!1),[O,L]=o.useState(!1),[t,m]=o.useState(new Date),[c,D]=o.useState(null),[N,k]=o.useState(null),[A,S]=o.useState(n),[M,_]=o.useState(!1);o.useState(!1);const[W,R]=o.useState("desc"),[B,T]=o.useState(!1);console.log("clubProfile",h);const E=s=>{const u=[...A].sort((a,d)=>{const v=new Date(a.clinic_date+" "+a.clinic_start_time),F=new Date(d.clinic_date+" "+d.clinic_start_time);return s==="asc"?v-F:F-v});S(u),R(s),T(!1)};o.useEffect(()=>{let s=[...n];c&&(s=s.filter(a=>{const d=new Date(a.clinic_date);return d.getDate()===c.getDate()&&d.getMonth()===c.getMonth()&&d.getFullYear()===c.getFullYear()})),w&&(s=s.filter(a=>parseInt(a.slots_remaining)>0));const u=s.sort((a,d)=>{const v=new Date(a.clinic_date+" "+a.clinic_start_time),F=new Date(d.clinic_date+" "+d.clinic_start_time);return W==="asc"?v-F:F-v});S(u)},[n,W,c,w]);const P=()=>{const s=Object.values(x.days).filter(Boolean).length,u=Object.values(x.timeOfDay).filter(Boolean).length,a=x.price.from||x.price.to?1:0;return s+u+a},r=async s=>{if(s)try{const u=new Date(t.getFullYear(),t.getMonth(),s,0,0,0);D(u);const a=u.toISOString().split("T")[0];let d=[];d.push(`start_date=${a}`),d.push(`end_date=${a}`);const v=Object.entries(x.days).filter(([I,Y])=>Y).map(([I])=>I.toLowerCase());v.length>0&&d.push(`weekday=${v.join(",")}`);const F=Object.entries(x.timeOfDay).filter(([I,Y])=>Y).map(([I])=>I.toLowerCase());F.length>0&&d.push(`times=${F.join(",")}`);const X=d.join("&");await b(X,a)}catch(u){console.error("Error handling date selection:",u)}},g=async()=>{D(null);const s=Object.entries(x.days).filter(([d,v])=>v).map(([d])=>d),u=Object.entries(x.timeOfDay).filter(([d,v])=>v).map(([d])=>d);let a="";s.length>0&&(a+=s.join(",")),u.length>0&&(a&&(a+=","),a+=u.join(",")),await b(a)},y=async()=>{_(!0);let s=[];if(c){const v=c.toISOString().split("T")[0];s.push(`start_date=${v}`),s.push(`end_date=${v}`)}const u=Object.entries(x.days).filter(([v,F])=>F).map(([v])=>v.toLowerCase());u.length>0&&s.push(`weekday=${u.join(",")}`);const a=Object.entries(x.timeOfDay).filter(([v,F])=>F).map(([v])=>v.toLowerCase());a.length>0&&s.push(`times=${a.join(",")}`);const d=s.join("&");await b(d,c?c.toISOString().split("T")[0]:null),L(!1),_(!1)},f=()=>{if($(!w),w)S(n);else{const s=n.filter(u=>parseInt(u.slots_remaining)>0);S(s)}},l=()=>{m(new Date(t.setMonth(t.getMonth()-1)))},i=()=>{m(new Date(t.setMonth(t.getMonth()+1)))};return e.jsx("div",{children:e.jsx("div",{className:"mx-auto max-w-6xl p-2 sm:p-4",children:e.jsxs("div",{className:"flex flex-col gap-4 sm:gap-6 md:flex-row md:gap-8",children:[e.jsxs("div",{className:"h-fit w-full rounded-lg bg-white p-3 shadow-sm sm:p-4 sm:shadow-5 md:w-[350px] lg:w-[400px]",children:[e.jsx(de,{clinics:n,currentMonth:t,selectedDate:c,onDateClick:r,onPreviousMonth:l,onNextMonth:i,onDateSelect:s=>{s&&(D(s),r(s.getDate()))},daysOff:(()=>{try{return h!=null&&h.days_off?JSON.parse(h.days_off):[]}catch(s){return console.error("Error parsing days_off:",s),[]}})()}),c&&e.jsxs("div",{className:"mt-3 flex flex-wrap items-center justify-between border-t border-gray-200 pt-3 sm:mt-4 sm:pt-4",children:[e.jsxs("span",{className:"mr-2 text-xs text-gray-600 sm:text-sm",children:["Showing clinics for"," ",c.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]}),e.jsx("button",{onClick:g,className:"text-xs text-blue-600 hover:underline sm:text-sm",children:"Clear"})]})]}),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"space-y-3 rounded-lg bg-white p-3 shadow-sm sm:space-y-4 sm:p-5",children:[e.jsx(q,{getActiveFiltersCount:P,clearFilters:C,setIsFilterModalOpen:L,availableOnly:w,toggleAvailableSlots:f,sortOrder:W,showSortOptions:B,setShowSortOptions:T,sortClinics:E}),A.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:w?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(P()>0||w)&&e.jsx("button",{onClick:()=>{C(),$(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):A.map(s=>e.jsx("div",{onClick:()=>k(s),children:e.jsx(V,{clinic:s})},s.id))]}),e.jsx(G,{isOpen:O,onClose:()=>L(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:y,className:"bg-gray-100",submitting:M,children:e.jsx(p,{filters:x,setFilters:j})}),e.jsx(J,{isOpen:N!==null,onClose:()=>k(null),clinic:N})]})]})})})}function he({programs:n,fetchClinics:b,FiltersContent:p,filters:x,setFilters:j,clearFilters:C}){const[h,w]=o.useState(!1),[$,O]=o.useState(!1),[L,t]=o.useState(null),[m,c]=o.useState(n),[D,N]=o.useState(!1);o.useState(!1);const[k,A]=o.useState("desc"),[S,M]=o.useState(!1),[_,W]=o.useState(0),R=l=>{const i=[...m].sort((s,u)=>{const a=new Date(s.clinic_date+" "+s.clinic_start_time),d=new Date(u.clinic_date+" "+u.clinic_start_time);return l==="asc"?a-d:d-a});c(i),A(l),M(!1)};o.useEffect(()=>{const l=[...n].sort((i,s)=>{const u=new Date(i.clinic_date+" "+i.clinic_start_time),a=new Date(s.clinic_date+" "+s.clinic_start_time);return k==="asc"?u-a:a-u});c(l)},[n,k]);const B=()=>{const l=Object.values(x.days).filter(Boolean).length,i=Object.values(x.timeOfDay).filter(Boolean).length,s=x.price.from||x.price.to?1:0;return l+i+s},T=async()=>{N(!0);let l=[];l.push(`week=${_}`);const i=Object.entries(x.days).filter(([a,d])=>d).map(([a])=>a.toLowerCase());i.length>0&&l.push(`weekday=${i.join(",")}`);const s=Object.entries(x.timeOfDay).filter(([a,d])=>d).map(([a])=>a.toLowerCase());s.length>0&&l.push(`times=${s.join(",")}`);const u=l.join("&");await b(null,!1,u),O(!1),N(!1)},E=()=>{if(w(!h),h)c(n);else{const l=n.filter(i=>parseInt(i.slots_remaining)>0);c(l)}},[P,r]=o.useState(me()),g=async()=>{if(_>0){const l=_-1;W(l),r(i=>i.clone().subtract(1,"week")),await b(null,!1,`filter=week+${l}`)}},y=async()=>{const l=_+1;W(l),r(i=>i.clone().add(1,"week")),await b(null,!1,`filter=week+${l}`)},f=()=>{const l=P.clone().startOf("week"),i=P.clone().endOf("week"),s=`${l.format("MMM D")} - ${i.format("MMM D")}`;return _===0?`This week (${s})`:_===1?`Next week (${s})`:`${_} weeks from now (${s})`};return o.useEffect(()=>{b(null,!1,"filter=week")},[]),e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mx-auto max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:p-4",children:[e.jsx("div",{className:"mx-auto mb-3 mt-3 w-fit max-w-xs rounded-xl bg-white p-1 shadow-sm sm:mb-5 sm:mt-5 sm:max-w-lg",children:e.jsxs("div",{className:"flex items-center justify-between gap-2 rounded-xl bg-gray-50 p-2 sm:gap-4",children:[e.jsx("button",{onClick:g,disabled:_===0,className:`rounded-xl bg-white p-1 text-gray-600 sm:p-2 ${_===0?"cursor-not-allowed opacity-50":"hover:text-gray-800"}`,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:"text-center text-sm font-medium sm:text-lg",children:f()}),e.jsx("button",{onClick:y,className:"rounded-xl bg-white p-1 text-gray-600 hover:text-gray-800 sm:p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})}),e.jsx(q,{getActiveFiltersCount:B,clearFilters:C,setIsFilterModalOpen:O,availableOnly:h,toggleAvailableSlots:E,sortOrder:k,showSortOptions:S,setShowSortOptions:M,sortClinics:R}),e.jsx("div",{className:"mx-auto mt-4 max-w-4xl space-y-3 sm:space-y-4",children:m.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No clinics found"}),e.jsx("p",{className:"px-4 text-center text-xs text-gray-500 sm:text-sm",children:h?"There are no clinics with available slots for the selected filters.":"There are no clinics matching your selected filters."}),(B()>0||h)&&e.jsx("button",{onClick:()=>{C(),w(!1)},className:"mt-4 text-xs font-medium text-blue-600 hover:text-blue-700 sm:text-sm",children:"Clear all filters"})]}):m.map(l=>e.jsx("div",{onClick:()=>t(l),children:e.jsx(V,{clinic:l})},l.id))})]}),e.jsx(G,{isOpen:$,onClose:()=>O(!1),title:"Filters",primaryButtonText:"Apply and close",onPrimaryAction:T,className:"bg-gray-100",submitting:D,children:e.jsx(p,{filters:x,setFilters:j})}),e.jsx(J,{isOpen:L!==null,onClose:()=>t(null),clinic:L})]})}let H=new ae,fe=new ne;function Z({filters:n,setFilters:b,customFilters:p=[],programs:x=[]}){const[j,C]=o.useState({dayOfWeek:!0,timeOfDay:!0,priceRange:!0,...p.reduce((t,m)=>({...t,[`custom_${m.id}`]:!0}),{})}),h=t=>{C(m=>({...m,[t]:!m[t]}))},w=t=>{b(m=>({...m,days:{...m.days,[t.toLowerCase()]:!m.days[t.toLowerCase()]}}))},$=t=>{b(m=>({...m,timeOfDay:{...m.timeOfDay,[t.toLowerCase()]:!m.timeOfDay[t.toLowerCase()]}}))},O=(t,m)=>{b(c=>({...c,price:{...c.price,[t]:m}}))},L=(t,m)=>{b(c=>{var D,N,k;return{...c,customFilters:{...c.customFilters,[t]:(N=(D=c.customFilters)==null?void 0:D[t])!=null&&N.includes(m)?c.customFilters[t].filter(A=>A!==m):[...((k=c.customFilters)==null?void 0:k[t])||[],m]}}})};return e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>h("dayOfWeek"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Day of week"}),e.jsx(z,{size:20,className:`text-gray-400 transition-transform duration-200 ${j.dayOfWeek?"rotate-180":""}`})]}),j.dayOfWeek&&e.jsx("div",{className:"space-y-3",children:["Weekend","Weekday","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"].map(t=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:n.days[t.toLowerCase()],onChange:()=>w(t),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:t})]},t))})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>h("timeOfDay"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Time of the day"}),e.jsx(z,{size:20,className:`text-gray-400 transition-transform duration-200 ${j.timeOfDay?"rotate-180":""}`})]}),j.timeOfDay&&e.jsx("div",{className:"space-y-3",children:["Morning","Afternoon","Evening"].map(t=>e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:n.timeOfDay[t.toLowerCase()],onChange:()=>$(t),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:t})]},t))})]}),e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>h("priceRange"),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:"Price range"}),e.jsx(z,{size:20,className:`text-gray-400 transition-transform duration-200 ${j.priceRange?"rotate-180":""}`})]}),j.priceRange&&e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"From"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"number",value:n.price.from,onChange:t=>O("from",t.target.value),className:"w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none",placeholder:"0.00"})]})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"To"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-500",children:"$"}),e.jsx("input",{type:"number",value:n.price.to,onChange:t=>O("to",t.target.value),className:"w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none",placeholder:"0.00"})]})]})]})]}),p.filter(t=>t.enabled).map(t=>{const m=[...new Set(x.map(c=>c[t.key]).filter(c=>c!=null&&c!==""))];return m.length===0?null:e.jsxs("div",{className:"space-y-3 rounded-xl border border-gray-200 bg-white p-5",children:[e.jsxs("button",{onClick:()=>h(`custom_${t.id}`),className:"flex w-full items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium",children:t.label}),e.jsx(z,{size:20,className:`text-gray-400 transition-transform duration-200 ${j[`custom_${t.id}`]?"rotate-180":""}`})]}),j[`custom_${t.id}`]&&e.jsx("div",{className:"space-y-3",children:m.map(c=>{var D,N;return e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:((N=(D=n.customFilters)==null?void 0:D[t.key])==null?void 0:N.includes(c))||!1,onChange:()=>L(t.key,c),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:t.key==="recurring"?c===1?"Yes":"No":c})]},c)})})]},t.id)})]})}function mt(){const[n,b]=o.useState(null),[p,x]=o.useState([]),[j,C]=o.useState(null),[h,w]=o.useState([]),[$,O]=o.useState([]),{dispatch:L}=o.useContext(ee),{dispatch:t}=o.useContext(te),[m,c]=o.useState([]),[D,N]=o.useState(!1),[k,A]=o.useState([]),[S,M]=o.useState({days:{weekend:!1,weekday:!1,sunday:!1,monday:!1,tuesday:!1,wednesday:!1,thursday:!1,friday:!1,saturday:!1},timeOfDay:{morning:!1,afternoon:!1,evening:!1},price:{from:"",to:""},customFilters:{}}),_=[{id:"table",label:"Table"},{id:"calendar",label:"Calendar"},{id:"weekly",label:"Weekly"}],W=localStorage.getItem("user"),R=async()=>{var r;try{const g=await H.getOne("user",W,{}),y=await H.getOne("clubs",g.model.club_id,{}),f=await H.getList("sports",{filter:[`club_id,eq,${g.model.club_id}`]});console.log("sportsResponse",f),x(f.list),C(y.model);let l="table";if((r=y.model)!=null&&r.clinic_description)try{const i=JSON.parse(y.model.clinic_description);i.default_view&&(l=i.default_view),i.custom_filters&&A(i.custom_filters)}catch(i){console.error("Error parsing clinic_description:",i)}b(l)}catch(g){console.error(g),b("table")}},B=async()=>{const r=await H.getList("coach",{join:["user|user_id"]}),g=await H.getList("user",{filter:["role,cs,user"]});w(r.list),O(g.list)},T=async(r,g=!1,y="")=>{N(!0);try{let f="/v3/api/custom/courtmatchup/user/clinics";if(y)f+=`?${y}`;else{let i=[];if(!g){if(r&&!isNaN(new Date(r).getTime())){const a=new Date(r);a.setHours(0,0,0,0);const d=new Date(a);d.setDate(d.getDate()+6),d.setHours(23,59,59,999),i.push(`start_date=${a.toISOString().split("T")[0]}`),i.push(`end_date=${d.toISOString().split("T")[0]}`)}const s=Object.entries(S.days).filter(([a,d])=>d).map(([a])=>a.toLowerCase());s.length>0&&i.push(`weekday=${s.join(",")}`);const u=Object.entries(S.timeOfDay).filter(([a,d])=>d).map(([a])=>a.toLowerCase());u.length>0&&i.push(`times=${u.join(",")}`),S.customFilters&&Object.entries(S.customFilters).forEach(([a,d])=>{d&&d.length>0&&i.push(`${a}=${d.join(",")}`)})}i.length===0&&i.push("week=0"),i.length>0&&(f+=`?${i.join("&")}`)}const l=await fe.callRawAPI(f,{},"GET");l.error||c(l==null?void 0:l.programs)}catch(f){console.log(f),le(L,f.message,"3000","error"),re(t,f.status)}finally{N(!1)}},E=async()=>{M({days:{weekend:!1,weekday:!1,sunday:!1,monday:!1,tuesday:!1,wednesday:!1,thursday:!1,friday:!1,saturday:!1},timeOfDay:{morning:!1,afternoon:!1,evening:!1},price:{from:"",to:""},customFilters:{}}),await T(null,!0)};o.useEffect(()=>{(async()=>(N(!0),await R(),await B(),N(!1),L({type:"SETPATH",payload:{path:"program-clinics"}})))()},[]),o.useEffect(()=>{n&&(async()=>(N(!0),await T(),N(!1)))()},[n]),console.log("programs",m);async function P(r){await E(),b(r)}return e.jsxs(e.Fragment,{children:[(D||!n)&&e.jsx(se,{}),n&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col justify-between bg-white px-3 py-3 sm:flex-row sm:items-center sm:px-4 sm:py-4",children:[e.jsx("h1",{className:"mb-3 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Clinics"}),e.jsx("div",{className:"mb-4 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-8 sm:text-sm",children:_.map(r=>e.jsx("button",{onClick:()=>P(r.id),className:`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${n===r.id?"bg-white-600":"bg-gray-100 text-gray-600"}`,children:r.label},r.id))})]}),e.jsx("div",{className:"px-2 py-2 sm:px-3 sm:py-3",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[n==="table"&&e.jsx(xe,{programs:m,fetchClinics:T,FiltersContent:r=>e.jsx(Z,{...r,customFilters:k,programs:m}),filters:S,setFilters:M,clearFilters:E}),n==="calendar"&&e.jsx(ue,{programs:m,fetchClinics:T,FiltersContent:r=>e.jsx(Z,{...r,customFilters:k,programs:m}),filters:S,setFilters:M,clubProfile:j,clearFilters:E}),n==="weekly"&&e.jsx(he,{programs:m,fetchClinics:T,FiltersContent:r=>e.jsx(Z,{...r,customFilters:k,programs:m}),filters:S,setFilters:M,clearFilters:E})]})})]})]})}export{mt as default};
