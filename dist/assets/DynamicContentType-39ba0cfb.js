import{j as a}from"./@nivo/heatmap-ba1ecfff.js";import{b as h}from"./vendor-851db8c1.js";import{M as v,ba as f}from"./index-97f6f167.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const b=new v,x="https://via.placeholder.com/150?text=%20",le=({contentType:c,contentValue:o,setContentValue:p})=>{const[u,i]=h.useState(x),y=async g=>{const d=new FormData;d.append("file",g.target.files[0]);try{const e=await b.uploadImage(d);i(e.url),p(e.url)}catch(e){console.error(e)}};switch(c){case"text":return a.jsx(a.Fragment,{children:a.jsx("textarea",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",rows:15,placeholder:"Content",onChange:g=>p(g.target.value),defaultValue:o})});case"image":return a.jsxs(a.Fragment,{children:[a.jsx("img",{src:f(o)?u:o,alt:"preview",height:150,width:150}),a.jsx("input",{type:"file",onChange:y,className:"shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]});case"number":return a.jsx("input",{type:"number",className:"shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",onChange:g=>p(g.target.value),defaultValue:o});case"team-list":return a.jsx(N,{setContentValue:p,contentValue:o});case"image-list":return a.jsx(k,{setContentValue:p,contentValue:o});case"captioned-image-list":return a.jsx(j,{setContentValue:p,contentValue:o});case"kvp":return a.jsx(I,{setContentValue:p,contentValue:o})}},k=({contentValue:c,setContentValue:o})=>{let p=[{key:"",value_type:"image",value:null}];f(c)||(p=JSON.parse(c));const[u,i]=h.useState(p),y=async d=>{const e=d.target.getAttribute("listkey"),t=new FormData;t.append("file",d.target.files[0]);try{const r=await b.uploadImage(t);i(l=>l.map((n,m)=>(m==e&&(n.value=r.url),n))),o(JSON.stringify(u))}catch(r){console.error(r)}},g=d=>{const e=d.target.getAttribute("listkey");i(t=>t.map((l,s)=>(s==e&&(l.key=d.target.value),l))),o(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((d,e)=>a.jsxs("div",{children:[a.jsx("img",{src:d.value!==null?d.value:x,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"key",listkey:e,onChange:g,defaultValue:d.key}),a.jsx("input",{listkey:e,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]})]},e*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:d=>i(e=>[...e,{key:"",value_type:"image",value:null}]),children:"+"})]})},j=({setContentValue:c,contentValue:o})=>{let p=[{key:"",value_type:"image",value:null,caption:""}];f(o)||(p=JSON.parse(o));const[u,i]=h.useState(p),y=async e=>{const t=e.target.getAttribute("listkey"),r=new FormData;r.append("file",e.target.files[0]);try{const l=await b.uploadImage(r);i(s=>s.map((m,w)=>(w==t&&(m.value=l.url),m))),c(JSON.stringify(u))}catch(l){console.error(l)}},g=e=>{const t=e.target.getAttribute("listkey");i(r=>r.map((s,n)=>(n==t&&(s.key=e.target.value),s))),c(JSON.stringify(u))},d=e=>{const t=e.target.getAttribute("listkey");i(r=>r.map((s,n)=>(n==t&&(s.caption=e.target.value),s))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((e,t)=>a.jsxs("div",{children:[a.jsx("img",{src:e.value!==null?e.value:x,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Key",listkey:t,onChange:g,defaultValue:e.key}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]}),a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Caption",listkey:t,onChange:d,defaultValue:e.caption})]},t*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:e=>i(t=>[...t,{key:"",value_type:"image",value:null,caption:""}]),children:"+"})]})},N=({setContentValue:c,contentValue:o})=>{let p=[{name:"",image:null,title:""}];f(o)||(p=JSON.parse(o));const[u,i]=h.useState(p),y=async e=>{const t=e.target.getAttribute("listkey"),r=new FormData;r.append("file",e.target.files[0]);try{const l=await b.uploadImage(r);i(s=>s.map((m,w)=>(w==t&&(m.image=l.url),m))),c(JSON.stringify(u))}catch(l){console.error(l)}},g=e=>{const t=e.target.getAttribute("listkey");i(r=>r.map((s,n)=>(n==t&&(s.name=e.target.value),s))),c(JSON.stringify(u))},d=e=>{const t=e.target.getAttribute("listkey");i(r=>r.map((s,n)=>(n==t&&(s.title=e.target.value),s))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block my-4",children:[u.map((e,t)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Title",listkey:t,onChange:d,defaultValue:e.title}),a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Name",listkey:t,onChange:g,defaultValue:e.name}),a.jsx("img",{src:e.image!==null?e.image:x,alt:"preview",height:150,width:150}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]},t*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:e=>i(t=>[...t,{name:"",image:null,title:""}]),children:"+"})]})},I=({setContentValue:c,contentValue:o})=>{let p=[{key:"",value_type:"text",value:""}];f(o)||(p=JSON.parse(o));const[u,i]=h.useState(p),y=[{key:"text",value:"Text"},{key:"number",value:"Number"},{key:"json",value:"JSON Object"},{key:"url",value:"URL"}],g=t=>{const r=t.target.getAttribute("listkey");i(l=>l.map((n,m)=>(m==r&&(n.key=t.target.value),n))),c(JSON.stringify(u))},d=t=>{const r=t.target.getAttribute("listkey");i(l=>l.map((n,m)=>(m==r&&(n.value=t.target.value),n))),c(JSON.stringify(u))},e=t=>{const r=t.target.getAttribute("listkey");i(l=>l.map((n,m)=>(m==r&&(n.value_type=t.target.value),n))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((t,r)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Key",listkey:r,onChange:g,defaultValue:t.key}),a.jsx("select",{className:"shadow block border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",listkey:r,onChange:e,defaultValue:t.value_type,children:y.map((l,s)=>a.jsx("option",{value:l.key,children:l.value},s*122))}),a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",required:!0,placeholder:"Value",listkey:r,onChange:d,defaultValue:t.value})]},r*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:t=>i(r=>[...r,{key:"",value_type:"text",value:""}]),children:"+"})]})};export{le as default};
