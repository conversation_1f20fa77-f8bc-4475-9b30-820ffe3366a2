import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,f as rt,b as J,L as Se}from"./vendor-851db8c1.js";import{M as ot,T as it,G as lt,A as ct,u as dt,o as mt,$ as ut,aC as pt,aD as H,an as ht,h as gt,b,x as P,aE as xt,d as yt,as as ft,E as vt,D as jt,H as wt}from"./index-97f6f167.js";import{B as Nt}from"./BackButton-11ba52b2.js";import{T as _t}from"./TimeSlots-9ce7c837.js";import{A as St}from"./AddPlayers-4c2a1b90.js";import{C as Ct}from"./Calendar-282b3fcf.js";import{S as bt}from"./SportTypeSelection-cc97819b.js";import{C as Ce}from"./ReservationSummary-c649f1d8.js";import{h as kt}from"./moment-a9aaa855.js";import{S as Tt}from"./react-select-c8303602.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";let $=new ot,Bt=new it;const Ss=({})=>{var he,ge,xe,ye,fe,ve,je,we,Ne,_e;const[d,be]=r.useState(null),[f,W]=r.useState(null),[I,Z]=r.useState(new Date),[ke,Te]=r.useState([]),[Be,Re]=r.useState([]),[L,Ee]=r.useState(0),[A,Pe]=r.useState(0),[Rt,U]=r.useState(!1),[w,T]=r.useState("main"),[p,K]=r.useState([]),[z,Ie]=r.useState(!1),[V,Q]=r.useState(1),[X,Le]=r.useState(!1),[ee,Fe]=r.useState(3.5),[te,Ae]=r.useState(3.5),[se,Oe]=r.useState(""),[Ye,ae]=r.useState(!1),[c,Me]=r.useState(null),[g,qe]=r.useState(null),[x,ne]=r.useState([]),[O,re]=r.useState([]),[v,oe]=r.useState(null),[He,$e]=r.useState(null),[Ve,De]=r.useState(null),[Ge,ie]=r.useState(!1),[Je,We]=r.useState(null),[le,Ze]=r.useState(null),[k,N]=r.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),{state:Et,dispatch:_}=r.useContext(lt);r.useContext(ct);const{club:s,pricing:ce,sports:B,loading:Pt,user_subscription:S,user_permissions:R,user_profile:y,club_membership:F,courts:Y}=dt(),Ue=rt();console.log("club",s),localStorage.getItem("user");const a=J.useMemo(()=>!(S!=null&&S.planId)||!(F!=null&&F.length)?null:F.find(t=>t.plan_id===S.planId),[S,F]),M=J.useMemo(()=>{var l,u;if(((l=a==null?void 0:a.advance_booking_enabled)==null?void 0:l.court)===!1){const j=new Date;return j.setFullYear(j.getFullYear()+10),j}const t=((u=a==null?void 0:a.advance_booking_days)==null?void 0:u.court)||10,n=new Date,o=new Date;return o.setDate(n.getDate()+t),o},[a]),Ke=async()=>{try{const t=await Bt.getList("user",{filter:["role,cs,user",`club_id,cs,${s==null?void 0:s.id}`]});Te(t.list)}catch(t){console.error(t)}},ze=async()=>{try{const t=await $.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");Re(t.groups)}catch(t){console.error(t)}};r.useEffect(()=>{(async()=>(ae(!0),await ze(),ae(!1)))()},[]),r.useEffect(()=>{Ke()},[s==null?void 0:s.id]),J.useEffect(()=>{mt({path:"/user/reserve-court",clubName:s==null?void 0:s.name,favicon:s==null?void 0:s.club_logo,description:"Reserve a court"})},[s==null?void 0:s.club_logo]),r.useEffect(()=>{},[w]);const Qe=()=>{Z(new Date(I.setMonth(I.getMonth()-1)))},Xe=()=>{Z(new Date(I.setMonth(I.getMonth()+1)))},i=B==null?void 0:B.find(t=>t.id===d),de=()=>{try{return s!=null&&s.custom_request_threshold?typeof s.custom_request_threshold=="object"?s.custom_request_threshold:JSON.parse(s.custom_request_threshold):[]}catch(t){return console.error("Error parsing custom request threshold:",t),[]}},C=(()=>{try{const t=de(),n=4;if(!Array.isArray(t)||!d)return n;const o=t.find(l=>l.id.toString()===d.toString());if(!o)return n;if(c&&o.sport_types){const l=o.sport_types.find(u=>u.type===c);if(l&&l.threshold>0)return l.threshold}return o.threshold>0?o.threshold:n}catch(t){return console.error("Error getting custom request threshold:",t),4}})();r.useEffect(()=>{d&&(console.log("Selected Sport:",d),console.log("Selected Type:",c),console.log("Selected SubType:",g),console.log("Max Players Allowed:",C),console.log("Custom Request Threshold Data:",de()))},[d,c,g,C]),r.useEffect(()=>{V>C-p.length&&Q(Math.max(0,C-p.length))},[C,p.length]);const{start_time:me,end_time:ue,duration:D}=ut(x);r.useEffect(()=>{if(Y&&Y.length>0){let t=[...Y];d&&(t=t.filter(n=>n.sport_id&&n.sport_id.toString()===d.toString())),c&&(t=t.filter(n=>n.type===c)),g&&(t=t.filter(n=>n.sub_type===g)),re(t)}else re([])},[Y,d,c,g]);const et=async()=>{var l,u,j;const t=(l=i==null?void 0:i.sport_types)==null?void 0:l.some(h=>h.type&&h.type.trim()!==""),n=(u=i==null?void 0:i.sport_types)==null?void 0:u.find(h=>h.type===c),o=(j=n==null?void 0:n.subtype)==null?void 0:j.some(h=>h&&h.trim()!=="");if(!d||t&&!c||t&&o&&!g||!f||!me||!ue){b(_,"Please select all required fields",3e3,"error");return}U(!0);try{const h=await $.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:A},"POST");$e(h.client_secret),De(h.payment_intent);const q=kt(f).format("YYYY-MM-DD"),m={sport_id:d,type:c,sub_type:g,date:q,start_time:me,end_time:ue,duration:D,reservation_type:ft.court,price:A,player_ids:p.map(nt=>nt.id),buddy_details:null,payment_status:0,payment_intent:h.payment_intent,service_fee:H(s==null?void 0:s.fee_settings,L),club_fee:s==null?void 0:s.club_fee,players_needed:V,min_ntrp:ee,max_ntrp:te,note:se};(s==null?void 0:s.allow_user_court_selection)===1&&v&&(m.court_id=v);const G=await $.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",m,"POST");return await vt($,{user_id:localStorage.getItem("user"),activity_type:jt.court_reservation,action_type:wt.CREATE,data:m,club_id:s==null?void 0:s.id,description:`${y==null?void 0:y.first_name} ${y==null?void 0:y.last_name} created a court reservation`}),G.error||b(_,"Reservation created successfully",3e3,"success"),Ze(G.reservation_id),G.booking_id}catch(h){console.error(h),b(_,h.message||"Error creating reservation",3e3,"error")}finally{U(!1)}},pe=t=>{K(n=>n.some(l=>l.id===t.id)?n.filter(l=>l.id!==t.id):[...n,t])},tt=()=>{T("players")};r.useEffect(()=>{if(p!=null&&p.length&&d&&c&&g&&(x!=null&&x.length)&&D){const t=pt({pricing:ce,sportId:d,type:c,subType:g,duration:D,selectedTime:x[0]}),n=H(s==null?void 0:s.fee_settings,t);Ee(t),Pe(t+n)}},[p,d,c,g,x,ce,s==null?void 0:s.fee_settings]);const st=async()=>{var l,u,j,h,q;if(!(S!=null&&S.planId)){N({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to reserve courts",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(R!=null&&R.allowCourt)){N({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${R==null?void 0:R.planName}) does not include court reservations. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(f>M&&((l=a==null?void 0:a.advance_booking_enabled)!=null&&l.court)){const m=`Your membership plan only allows booking ${((u=a==null?void 0:a.advance_booking_days)==null?void 0:u.court)||10} days in advance. Please select a valid date.`;N({isOpen:!0,title:"Date Selection Error",message:m,type:"warning"}),T("main");return}const t=(j=i==null?void 0:i.sport_types)==null?void 0:j.some(m=>m.type&&m.type.trim()!==""),n=(h=i==null?void 0:i.sport_types)==null?void 0:h.find(m=>m.type===c),o=(q=n==null?void 0:n.subtype)==null?void 0:q.some(m=>m&&m.trim()!=="");if(!d||t&&!c||t&&o&&!g||!f||!x.length){N({isOpen:!0,title:"Incomplete Details",message:"Please complete all required Reservation detail",type:"warning"}),T("main");return}if((s==null?void 0:s.allow_user_court_selection)===1&&!v){N({isOpen:!0,title:"Court Selection Required",message:"Please select a court for your reservation",type:"warning"});return}if(!p.length){N({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}try{ie(!0);const m=await et();if(!m)throw new Error("Failed to create reservation");We(m),T("payment")}catch(m){console.error("Reservation error:",m),N({isOpen:!0,title:"Reservation Error",message:m.message||"Error creating reservation",type:"error"})}finally{ie(!1)}},at=t=>{ne([{from:t.from,until:t.until}])},E=s!=null&&s.court_description?JSON.parse(s==null?void 0:s.court_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"",children:[e.jsx(ht,{isOpen:k.isOpen,onClose:()=>N({...k,isOpen:!1}),title:k.title,message:k.message,actionButtonText:k.actionButtonText,actionButtonLink:k.actionButtonLink,type:k.type}),Ye&&e.jsx(gt,{}),e.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[w==="main"&&e.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),w==="players"&&e.jsx("div",{className:" ",children:"Step 2 • Reservation detail"}),w==="payment"&&e.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),e.jsxs("div",{className:"p-4",children:[e.jsx(Nt,{onBack:()=>{w==="main"?Ue(-1):T(w==="payment"?"players":"main")}}),w==="main"?e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(bt,{sports:B,onSelectionChange:({sport:t,type:n,subType:o})=>{be(t),Me(n),qe(o),W(null),ne([]),oe(null)}}),d&&(!((he=i==null?void 0:i.sport_types)!=null&&he.length)||c!==null&&(g!==null||!((ye=(xe=(ge=i==null?void 0:i.sport_types)==null?void 0:ge.find(t=>t.type===c))==null?void 0:xe.subtype)!=null&&ye.length)))?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[(fe=a==null?void 0:a.advance_booking_enabled)!=null&&fe.court?e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can reserve a court up to"," ",(ve=a==null?void 0:a.advance_booking_days)==null?void 0:ve.court," ",((je=a==null?void 0:a.advance_booking_days)==null?void 0:je.court)===1?"day":"days"," ","in advance."]}):e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a court for any future date."}),e.jsx(Ct,{currentMonth:I,selectedDate:f,onDateSelect:t=>{var n,o;if(t>M){const l=(n=a==null?void 0:a.advance_booking_enabled)!=null&&n.court?`Your membership plan only allows booking ${((o=a==null?void 0:a.advance_booking_days)==null?void 0:o.court)||10} days in advance`:"";if(l){b(_,l,3e3,"warning");return}}W(t)},onPreviousMonth:Qe,onNextMonth:Xe,daysOff:s!=null&&s.days_off?JSON.parse(s.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:M,disabledDateMessage:(we=a==null?void 0:a.advance_booking_enabled)!=null&&we.court?`Your membership plan only allows booking ${((Ne=a==null?void 0:a.advance_booking_days)==null?void 0:Ne.court)||10} days in advance`:"You can book for any future date"})]}),f&&e.jsx(e.Fragment,{children:e.jsx(_t,{selectedDate:f,timeRange:x,onTimeClick:at,onNext:()=>{var t,n;if(!x.length){b(_,"Please select a time slot",3e3,"error");return}if(f>M){const o=(t=a==null?void 0:a.advance_booking_enabled)!=null&&t.court?`Your membership plan only allows booking ${((n=a==null?void 0:a.advance_booking_days)==null?void 0:n.court)||10} days in advance`:"";if(o){b(_,o,3e3,"warning");return}}tt()},nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,className:"h-fit",isTimeSlotAvailable:()=>!0,clubTimes:s!=null&&s.times?JSON.parse(s.times):[]})})]}):e.jsx("div",{className:"flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5 md:col-span-2",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available time slots"})]})})]})})})}):w==="payment"?e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(Ce,{selectedSport:d,sports:B,selectedType:c,selectedSubType:g,selectedDate:f,selectedTimes:x,selectedCourt:v?O.find(t=>t.id===v):null})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Club fee"}),e.jsx("span",{children:P(L)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:P(H(s==null?void 0:s.fee_settings,L))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:P(A)})]}),e.jsxs("div",{children:[e.jsx(xt,{user:y,bookingId:Je,reservationId:le,clientSecret:He,paymentIntent:Ve,navigateRoute:`/user/payment-success/${le}?type=court`}),e.jsx("div",{className:"mt-4",children:e.jsx("div",{className:"text-sm text-gray-500",children:E==null?void 0:E.payment_description})})]}),e.jsx("div",{className:"space-y-4 text-sm text-gray-500",children:e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(Se,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(Se,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]})})]})]})})]})]}):e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-4",children:[" ",e.jsx(Ce,{selectedSport:d,sports:B,selectedType:c,selectedSubType:g,selectedDate:f,selectedTimes:x,selectedCourt:v?O.find(t=>t.id===v):null}),(s==null?void 0:s.allow_user_court_selection)===1&&e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Select Court"}),e.jsx(Tt,{className:"w-full text-sm",options:O.map(t=>({value:t.id,label:t.name})),onChange:t=>oe(t?t.value:null),value:v?{value:v,label:((_e=O.find(t=>t.id===v))==null?void 0:_e.name)||"Selected Court"}:null,isClearable:!0,placeholder:"Select a court",noOptionsMessage:()=>{var n,o,l;if(!d)return"Please select a sport first";if((n=i==null?void 0:i.sport_types)!=null&&n.some(u=>u.type&&u.type.trim()!=="")&&!c)return"Please select a type";const t=(o=i==null?void 0:i.sport_types)==null?void 0:o.find(u=>u.type===c);return(l=t==null?void 0:t.subtype)!=null&&l.some(u=>u&&u.trim()!=="")&&!g?"Please select a sub-type":"No courts available for the selected criteria"}}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Select your preferred court for this reservation"})]})]}),e.jsx(St,{players:ke,groups:Be,selectedPlayers:p,onPlayerToggle:t=>{if(p.some(o=>o.id===t.id)){if(t.id===(y==null?void 0:y.id)){b(_,"You cannot remove yourself from the reservation",3e3,"warning");return}pe(t);return}if(p.length>=C){const o=(i==null?void 0:i.name)||"this sport";b(_,`Maximum ${C} players allowed for ${o} (including yourself)`,3e3,"warning");return}pe(t)},isFindBuddyEnabled:z,setSelectedPlayers:K,onFindBuddyToggle:()=>{Ie(!z),Le(!X)},playersNeeded:V,onPlayersNeededChange:Q,maximumPlayers:C,userProfile:y,showPlayersNeeded:X,onNtrpMinChange:Fe,onNtrpMaxChange:Ae,onShortBioChange:Oe,initialNtrpMin:ee,initialNtrpMax:te,initialShortBio:se}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reservation detail"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",p==null?void 0:p.length,")"]}),e.jsx("div",{className:"mt-1",children:p.length>0&&p.map(t=>e.jsxs("div",{className:"text-sm",children:[t.first_name," ",t.last_name]},t.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Club Fee"}),e.jsx("span",{children:P(L)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:P(H(s==null?void 0:s.fee_settings,L))})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:P(A)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(yt,{loading:Ge,onClick:st,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("div",{className:"text-center text-sm text-gray-500",children:E==null?void 0:E.reservation_description}),e.jsx("div",{className:"space-y-2 text-center text-sm text-gray-500",children:e.jsx("p",{children:"(You will not be charged yet)"})})]})})]})]})]})]})};export{Ss as default};
