import{b as u}from"../vendor-851db8c1.js";import{P as s}from"../@fortawesome/react-fontawesome-13437837.js";function B(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable})),t.push.apply(t,n)}return t}function M(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?B(Object(t),!0).forEach(function(n){K(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):B(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function w(r){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?w=function(e){return typeof e}:w=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(r)}function K(r,e,t){return e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Y(r,e){return ae(r)||ue(r,e)||se(r,e)||ie()}function ae(r){if(Array.isArray(r))return r}function ue(r,e){var t=r&&(typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"]);if(t!=null){var n=[],i=!0,a=!1,p,m;try{for(t=t.call(r);!(i=(p=t.next()).done)&&(n.push(p.value),!(e&&n.length===e));i=!0);}catch(o){a=!0,m=o}finally{try{!i&&t.return!=null&&t.return()}finally{if(a)throw m}}return n}}function se(r,e){if(r){if(typeof r=="string")return q(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);if(t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set")return Array.from(r);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return q(r,e)}}function q(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=r[t];return n}function ie(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var v=function(e,t,n){var i=!!n,a=u.useRef(n);u.useEffect(function(){a.current=n},[n]),u.useEffect(function(){if(!i||!e)return function(){};var p=function(){a.current&&a.current.apply(a,arguments)};return e.on(t,p),function(){e.off(t,p)}},[i,t,e,a])},I=function(e){var t=u.useRef(e);return u.useEffect(function(){t.current=e},[e]),t.current},x=function(e){return e!==null&&w(e)==="object"},ce=function(e){return x(e)&&typeof e.then=="function"},le=function(e){return x(e)&&typeof e.elements=="function"&&typeof e.createToken=="function"&&typeof e.createPaymentMethod=="function"&&typeof e.confirmCardPayment=="function"},$="[object Object]",pe=function r(e,t){if(!x(e)||!x(t))return e===t;var n=Array.isArray(e),i=Array.isArray(t);if(n!==i)return!1;var a=Object.prototype.toString.call(e)===$,p=Object.prototype.toString.call(t)===$;if(a!==p)return!1;if(!a&&!n)return e===t;var m=Object.keys(e),o=Object.keys(t);if(m.length!==o.length)return!1;for(var C={},y=0;y<m.length;y+=1)C[m[y]]=!0;for(var g=0;g<o.length;g+=1)C[o[g]]=!0;var f=Object.keys(C);if(f.length!==m.length)return!1;var k=e,b=t,S=function(P){return r(k[P],b[P])};return f.every(S)},F=function(e,t,n){return x(e)?Object.keys(e).reduce(function(i,a){var p=!x(t)||!pe(e[a],t[a]);return n.includes(a)?(p&&console.warn("Unsupported prop change: options.".concat(a," is not a mutable property.")),i):p?M(M({},i||{}),{},K({},a,e[a])):i},null):null},J="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",D=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:J;if(e===null||le(e))return e;throw new Error(t)},fe=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:J;if(ce(e))return{tag:"async",stripePromise:Promise.resolve(e).then(function(i){return D(i,t)})};var n=D(e,t);return n===null?{tag:"empty"}:{tag:"sync",stripe:n}},de=function(e){!e||!e._registerWrapper||!e.registerAppInfo||(e._registerWrapper({name:"react-stripe-js",version:"2.8.1"}),e.registerAppInfo({name:"react-stripe-js",version:"2.8.1",url:"https://stripe.com/docs/stripe-js/react"}))},N=u.createContext(null);N.displayName="ElementsContext";var z=function(e,t){if(!e)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},me=function(e){var t=e.stripe,n=e.options,i=e.children,a=u.useMemo(function(){return fe(t)},[t]),p=u.useState(function(){return{stripe:a.tag==="sync"?a.stripe:null,elements:a.tag==="sync"?a.stripe.elements(n):null}}),m=Y(p,2),o=m[0],C=m[1];u.useEffect(function(){var f=!0,k=function(S){C(function(O){return O.stripe?O:{stripe:S,elements:S.elements(n)}})};return a.tag==="async"&&!o.stripe?a.stripePromise.then(function(b){b&&f&&k(b)}):a.tag==="sync"&&!o.stripe&&k(a.stripe),function(){f=!1}},[a,o,n]);var y=I(t);u.useEffect(function(){y!==null&&y!==t&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[y,t]);var g=I(n);return u.useEffect(function(){if(o.elements){var f=F(n,g,["clientSecret","fonts"]);f&&o.elements.update(f)}},[n,g,o.elements]),u.useEffect(function(){de(o.stripe)},[o.stripe]),u.createElement(N.Provider,{value:o},i)};me.propTypes={stripe:s.any,options:s.object};var ve=function(e){var t=u.useContext(N);return z(t,e)},be=function(){var e=ve("calls useElements()"),t=e.elements;return t};s.func.isRequired;var H=u.createContext(null);H.displayName="CustomCheckoutSdkContext";var ye=function(e,t){if(!e)throw new Error("Could not find CustomCheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CustomCheckoutProvider> provider."));return e},he=u.createContext(null);he.displayName="CustomCheckoutContext";s.any,s.shape({clientSecret:s.string.isRequired,elementsOptions:s.object}).isRequired;var L=function(e){var t=u.useContext(H),n=u.useContext(N);if(t&&n)throw new Error("You cannot wrap the part of your app that ".concat(e," in both <CustomCheckoutProvider> and <Elements> providers."));return t?ye(t,e):z(n,e)},Ce=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},c=function(e,t){var n="".concat(Ce(e),"Element"),i=function(o){var C=o.id,y=o.className,g=o.options,f=g===void 0?{}:g,k=o.onBlur,b=o.onFocus,S=o.onReady,O=o.onChange,P=o.onEscape,V=o.onClick,G=o.onLoadError,Q=o.onLoaderStart,X=o.onNetworksChange,Z=o.onConfirm,ee=o.onCancel,te=o.onShippingAddressChange,re=o.onShippingRateChange,A=L("mounts <".concat(n,">")),j="elements"in A?A.elements:null,R="customCheckoutSdk"in A?A.customCheckoutSdk:null,ne=u.useState(null),W=Y(ne,2),d=W[0],oe=W[1],E=u.useRef(null),T=u.useRef(null);v(d,"blur",k),v(d,"focus",b),v(d,"escape",P),v(d,"click",V),v(d,"loaderror",G),v(d,"loaderstart",Q),v(d,"networkschange",X),v(d,"confirm",Z),v(d,"cancel",ee),v(d,"shippingaddresschange",te),v(d,"shippingratechange",re),v(d,"change",O);var U;S&&(e==="expressCheckout"?U=S:U=function(){S(d)}),v(d,"ready",U),u.useLayoutEffect(function(){if(E.current===null&&T.current!==null&&(j||R)){var h=null;R?h=R.createElement(e,f):j&&(h=j.create(e,f)),E.current=h,oe(h),h&&h.mount(T.current)}},[j,R,f]);var _=I(f);return u.useEffect(function(){if(E.current){var h=F(f,_,["paymentRequest"]);h&&"update"in E.current&&E.current.update(h)}},[f,_]),u.useLayoutEffect(function(){return function(){if(E.current&&typeof E.current.destroy=="function")try{E.current.destroy(),E.current=null}catch{}}},[]),u.createElement("div",{id:C,className:y,ref:T})},a=function(o){L("mounts <".concat(n,">"));var C=o.id,y=o.className;return u.createElement("div",{id:C,className:y})},p=t?a:i;return p.propTypes={id:s.string,className:s.string,onChange:s.func,onBlur:s.func,onFocus:s.func,onReady:s.func,onEscape:s.func,onClick:s.func,onLoadError:s.func,onLoaderStart:s.func,onNetworksChange:s.func,onConfirm:s.func,onCancel:s.func,onShippingAddressChange:s.func,onShippingRateChange:s.func,options:s.object},p.displayName=n,p.__elementType=e,p},l=typeof window>"u",ge=u.createContext(null);ge.displayName="EmbeddedCheckoutProviderContext";var ke=function(){var e=L("calls useStripe()"),t=e.stripe;return t};c("auBankAccount",l);var xe=c("card",l);c("cardNumber",l);c("cardExpiry",l);c("cardCvc",l);c("fpxBank",l);c("iban",l);c("idealBank",l);c("p24Bank",l);c("epsBank",l);var Oe=c("payment",l);c("expressCheckout",l);c("currencySelector",l);c("paymentRequestButton",l);c("linkAuthentication",l);c("address",l);c("shippingAddress",l);c("paymentMethodMessaging",l);c("affirmMessage",l);c("afterpayClearpayMessage",l);export{xe as C,me as E,Oe as P,be as a,ke as u};
