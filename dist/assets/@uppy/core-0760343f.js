import{e as ae,t as le,T as de,n as ue,B as ce}from"./aws-s3-c5961f7a.js";import{g as ee,p as _}from"./compressor-11f993e4.js";import{g as he}from"../vendor-851db8c1.js";import{D as J}from"../@fullcalendar/core-8ccc1ac4.js";function q(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var pe=0;function te(r){return"__private_"+pe+++"_"+r}const fe={version:"3.2.2"};var E=te("callbacks"),Y=te("publish");class se{constructor(){Object.defineProperty(this,Y,{value:ge}),this.state={},Object.defineProperty(this,E,{writable:!0,value:new Set})}getState(){return this.state}setState(e){const t={...this.state},s={...this.state,...e};this.state=s,q(this,Y)[Y](t,s,e)}subscribe(e){return q(this,E)[E].add(e),()=>{q(this,E)[E].delete(e)}}}function ge(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];q(this,E)[E].forEach(s=>{s(...e)})}se.VERSION=fe.version;const Z={__proto__:null,md:"text/markdown",markdown:"text/markdown",mp4:"video/mp4",mp3:"audio/mp3",svg:"image/svg+xml",jpg:"image/jpeg",png:"image/png",webp:"image/webp",gif:"image/gif",heic:"image/heic",heif:"image/heif",yaml:"text/yaml",yml:"text/yaml",csv:"text/csv",tsv:"text/tab-separated-values",tab:"text/tab-separated-values",avi:"video/x-msvideo",mks:"video/x-matroska",mkv:"video/x-matroska",mov:"video/quicktime",dicom:"application/dicom",doc:"application/msword",docm:"application/vnd.ms-word.document.macroenabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroenabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",xla:"application/vnd.ms-excel",xlam:"application/vnd.ms-excel.addin.macroenabled.12",xlc:"application/vnd.ms-excel",xlf:"application/x-xliff+xml",xlm:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroenabled.12",xlsm:"application/vnd.ms-excel.sheet.macroenabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroenabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xlw:"application/vnd.ms-excel",txt:"text/plain",text:"text/plain",conf:"text/plain",log:"text/plain",pdf:"application/pdf",zip:"application/zip","7z":"application/x-7z-compressed",rar:"application/x-rar-compressed",tar:"application/x-tar",gz:"application/gzip",dmg:"application/x-apple-diskimage"};function ie(r){var e;if(r.type)return r.type;const t=r.name?(e=ee(r.name).extension)==null?void 0:e.toLowerCase():null;return t&&t in Z?Z[t]:"application/octet-stream"}function me(r){return r.charCodeAt(0).toString(32)}function Q(r){let e="";return r.replace(/[^A-Z0-9]/gi,t=>(e+=`-${me(t)}`,"/"))+e}function ve(r,e){let t=e||"uppy";return typeof r.name=="string"&&(t+=`-${Q(r.name.toLowerCase())}`),r.type!==void 0&&(t+=`-${r.type}`),r.meta&&typeof r.meta.relativePath=="string"&&(t+=`-${Q(r.meta.relativePath.toLowerCase())}`),r.data.size!==void 0&&(t+=`-${r.data.size}`),r.data.lastModified!==void 0&&(t+=`-${r.data.lastModified}`),t}function we(r){return!r.isRemote||!r.remote?!1:new Set(["box","dropbox","drive","facebook","unsplash"]).has(r.remote.provider)}function ye(r,e){if(we(r))return r.id;const t=ie(r);return ve({...r,type:t},e)}function be(r){if(r==null&&typeof navigator<"u"&&(r=navigator.userAgent),!r)return!0;const e=/Edge\/(\d+\.\d+)/.exec(r);if(!e)return!0;const s=e[1].split(".",2),i=parseInt(s[0],10),o=parseInt(s[1],10);return i<15||i===15&&o<15063||i>18||i===18&&o>=18218}function Fe(r,e){return e.name?e.name:r.split("/")[0]==="image"?`${r.split("/")[0]}.${r.split("/")[1]}`:"noname"}function H(r){return r<10?`0${r}`:r.toString()}function k(){const r=new Date,e=H(r.getHours()),t=H(r.getMinutes()),s=H(r.getSeconds());return`${e}:${t}:${s}`}const Se={debug:()=>{},warn:()=>{},error:function(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];return console.error(`[Uppy] [${k()}]`,...e)}},Pe={debug:function(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];return console.debug(`[Uppy] [${k()}]`,...e)},warn:function(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];return console.warn(`[Uppy] [${k()}]`,...e)},error:function(){for(var r=arguments.length,e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];return console.error(`[Uppy] [${k()}]`,...e)}};function re(r,e){this.text=r=r||"",this.hasWild=~r.indexOf("*"),this.separator=e,this.parts=r.split(e)}re.prototype.match=function(r){var e=!0,t=this.parts,s,i=t.length,o;if(typeof r=="string"||r instanceof String)if(!this.hasWild&&this.text!=r)e=!1;else{for(o=(r||"").split(this.separator),s=0;e&&s<i;s++)t[s]!=="*"&&(s<o.length?e=t[s]===o[s]:e=!1);e=e&&o}else if(typeof r.splice=="function")for(e=[],s=r.length;s--;)this.match(r[s])&&(e[e.length]=r[s]);else if(typeof r=="object"){e={};for(var n in r)this.match(n)&&(e[n]=r[n])}return e};var Ue=function(r,e,t){var s=new re(r,t||/[\/\.]/);return typeof e<"u"?s.match(e):s},xe=Ue,Oe=/[\/\+\.]/,Ee=function(r,e){function t(s){var i=xe(s,r,Oe);return i&&i.length>=2}return e?t(e.split(";")[0]):t};const Te=he(Ee),Ae={maxFileSize:null,minFileSize:null,maxTotalFileSize:null,maxNumberOfFiles:null,minNumberOfFiles:null,allowedFileTypes:null,requiredMetaFields:[]};class w extends Error{constructor(e,t){var s;super(e),this.isRestriction=!0,this.isUserFacing=(s=t==null?void 0:t.isUserFacing)!=null?s:!0,t!=null&&t.file&&(this.file=t.file)}}class $e{constructor(e,t){this.getI18n=t,this.getOpts=()=>{var s;const i=e();if(((s=i.restrictions)==null?void 0:s.allowedFileTypes)!=null&&!Array.isArray(i.restrictions.allowedFileTypes))throw new TypeError("`restrictions.allowedFileTypes` must be an array");return i}}validateAggregateRestrictions(e,t){const{maxTotalFileSize:s,maxNumberOfFiles:i}=this.getOpts().restrictions;if(i&&e.filter(n=>!n.isGhost).length+t.length>i)throw new w(`${this.getI18n()("youCanOnlyUploadX",{smart_count:i})}`);if(s){const o=[...e,...t].reduce((n,a)=>{var u;return n+((u=a.size)!=null?u:0)},0);if(o>s)throw new w(this.getI18n()("aggregateExceedsSize",{sizeAllowed:_(s),size:_(o)}))}}validateSingleFile(e){const{maxFileSize:t,minFileSize:s,allowedFileTypes:i}=this.getOpts().restrictions;if(i&&!i.some(n=>n.includes("/")?e.type?Te(e.type.replace(/;.*?$/,""),n):!1:n[0]==="."&&e.extension?e.extension.toLowerCase()===n.slice(1).toLowerCase():!1)){const n=i.join(", ");throw new w(this.getI18n()("youCanOnlyUploadFileTypes",{types:n}),{file:e})}if(t&&e.size!=null&&e.size>t)throw new w(this.getI18n()("exceedsSize",{size:_(t),file:e.name}),{file:e});if(s&&e.size!=null&&e.size<s)throw new w(this.getI18n()("inferiorSize",{size:_(s)}),{file:e})}validate(e,t){t.forEach(s=>{this.validateSingleFile(s)}),this.validateAggregateRestrictions(e,t)}validateMinNumberOfFiles(e){const{minNumberOfFiles:t}=this.getOpts().restrictions;if(t&&Object.keys(e).length<t)throw new w(this.getI18n()("youHaveToAtLeastSelectX",{smart_count:t}))}getMissingRequiredMetaFields(e){const t=new w(this.getI18n()("missingRequiredMetaFieldOnFile",{fileName:e.name})),{requiredMetaFields:s}=this.getOpts().restrictions,i=[];for(const o of s)(!Object.hasOwn(e.meta,o)||e.meta[o]==="")&&i.push(o);return{missingFields:i,error:t}}}const je={strings:{addBulkFilesFailed:{0:"Failed to add %{smart_count} file due to an internal error",1:"Failed to add %{smart_count} files due to internal errors"},youCanOnlyUploadX:{0:"You can only upload %{smart_count} file",1:"You can only upload %{smart_count} files"},youHaveToAtLeastSelectX:{0:"You have to select at least %{smart_count} file",1:"You have to select at least %{smart_count} files"},aggregateExceedsSize:"You selected %{size} of files, but maximum allowed size is %{sizeAllowed}",exceedsSize:"%{file} exceeds maximum allowed size of %{size}",missingRequiredMetaField:"Missing required meta fields",missingRequiredMetaFieldOnFile:"Missing required meta fields in %{fileName}",inferiorSize:"This file is smaller than the allowed size of %{size}",youCanOnlyUploadFileTypes:"You can only upload: %{types}",noMoreFilesAllowed:"Cannot add more files",noDuplicates:"Cannot add the duplicate file '%{fileName}', it already exists",companionError:"Connection with Companion failed",authAborted:"Authentication aborted",companionUnauthorizeHint:"To unauthorize to your %{provider} account, please go to %{url}",failedToUpload:"Failed to upload %{file}",noInternetConnection:"No Internet connection",connectedToInternet:"Connected to the Internet",noFilesFound:"You have no files or folders here",noSearchResults:"Unfortunately, there are no results for this search",selectX:{0:"Select %{smart_count}",1:"Select %{smart_count}"},allFilesFromFolderNamed:"All files from folder %{name}",openFolderNamed:"Open folder %{name}",cancel:"Cancel",logOut:"Log out",filter:"Filter",resetFilter:"Reset filter",loading:"Loading...",loadedXFiles:"Loaded %{numFiles} files",authenticateWithTitle:"Please authenticate with %{pluginName} to select files",authenticateWith:"Connect to %{pluginName}",signInWithGoogle:"Sign in with Google",searchImages:"Search for images",enterTextToSearch:"Enter text to search for images",search:"Search",resetSearch:"Reset search",emptyFolderAdded:"No files were added from empty folder",addedNumFiles:"Added %{numFiles} file(s)",folderAlreadyAdded:'The folder "%{folder}" was already added',folderAdded:{0:"Added %{smart_count} file from %{folder}",1:"Added %{smart_count} files from %{folder}"},additionalRestrictionsFailed:"%{count} additional restrictions were not fulfilled",unnamed:"Unnamed"}};let oe,ne;function l(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Ne=0;function h(r){return"__private_"+Ne+++"_"+r}const Me={version:"3.13.1"},L={totalProgress:0,allowNewUpload:!0,error:null,recoveredState:null};var p=h("plugins"),m=h("restricter"),j=h("storeUnsubscribe"),F=h("emitter"),T=h("preProcessors"),A=h("uploaders"),U=h("postProcessors"),v=h("informAndEmit"),I=h("checkRequiredMetaFieldsOnFile"),V=h("checkRequiredMetaFields"),N=h("assertNewUploadAllowed"),K=h("transformFile"),M=h("startIfAutoProceed"),C=h("checkAndUpdateFileState"),W=h("addListeners"),y=h("updateOnlineStatus"),R=h("requestClientById"),S=h("createUpload"),X=h("getUpload"),$=h("removeUpload"),P=h("runUpload");oe=Symbol.for("uppy test: getPlugins");ne=Symbol.for("uppy test: createUpload");class G{constructor(e){Object.defineProperty(this,P,{value:He}),Object.defineProperty(this,$,{value:Ye}),Object.defineProperty(this,X,{value:Ge}),Object.defineProperty(this,S,{value:Be}),Object.defineProperty(this,W,{value:ke}),Object.defineProperty(this,C,{value:qe}),Object.defineProperty(this,M,{value:Le}),Object.defineProperty(this,K,{value:_e}),Object.defineProperty(this,N,{value:Ie}),Object.defineProperty(this,V,{value:ze}),Object.defineProperty(this,I,{value:Re}),Object.defineProperty(this,v,{value:Ce}),Object.defineProperty(this,p,{writable:!0,value:Object.create(null)}),Object.defineProperty(this,m,{writable:!0,value:void 0}),Object.defineProperty(this,j,{writable:!0,value:void 0}),Object.defineProperty(this,F,{writable:!0,value:ae()}),Object.defineProperty(this,T,{writable:!0,value:new Set}),Object.defineProperty(this,A,{writable:!0,value:new Set}),Object.defineProperty(this,U,{writable:!0,value:new Set}),this.scheduledAutoProceed=null,this.wasOffline=!1,this.calculateProgress=le((i,o)=>{const n=this.getFile(i==null?void 0:i.id);if(i==null||!n){this.log(`Not setting progress for a file that has been removed: ${i==null?void 0:i.id}`);return}if(n.progress.percentage===100){this.log(`Not setting progress for a file that has been already uploaded: ${i.id}`);return}const a=Number.isFinite(o.bytesTotal)&&o.bytesTotal>0;this.setFileState(i.id,{progress:{...n.progress,bytesUploaded:o.bytesUploaded,bytesTotal:o.bytesTotal,percentage:a?Math.round(o.bytesUploaded/o.bytesTotal*100):0}}),this.calculateTotalProgress()},500,{leading:!0,trailing:!0}),Object.defineProperty(this,y,{writable:!0,value:this.updateOnlineStatus.bind(this)}),Object.defineProperty(this,R,{writable:!0,value:new Map}),this.defaultLocale=je;const t={id:"uppy",autoProceed:!1,allowMultipleUploadBatches:!0,debug:!1,restrictions:Ae,meta:{},onBeforeFileAdded:(i,o)=>!Object.hasOwn(o,i.id),onBeforeUpload:i=>i,store:new se,logger:Se,infoTimeout:5e3},s={...t,...e};this.opts={...s,restrictions:{...t.restrictions,...e&&e.restrictions}},e&&e.logger&&e.debug?this.log("You are using a custom `logger`, but also set `debug: true`, which uses built-in logger to output logs to console. Ignoring `debug: true` and using your custom `logger`.","warning"):e&&e.debug&&(this.opts.logger=Pe),this.log(`Using Core v${G.VERSION}`),this.i18nInit(),this.store=this.opts.store,this.setState({...L,plugins:{},files:{},currentUploads:{},capabilities:{uploadProgress:be(),individualCancellation:!0,resumableUploads:!1},meta:{...this.opts.meta},info:[]}),l(this,m)[m]=new $e(()=>this.opts,()=>this.i18n),l(this,j)[j]=this.store.subscribe((i,o,n)=>{this.emit("state-update",i,o,n),this.updateAll(o)}),this.opts.debug&&typeof window<"u"&&(window[this.opts.id]=this),l(this,W)[W]()}emit(e){for(var t=arguments.length,s=new Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];l(this,F)[F].emit(e,...s)}on(e,t){return l(this,F)[F].on(e,t),this}once(e,t){return l(this,F)[F].once(e,t),this}off(e,t){return l(this,F)[F].off(e,t),this}updateAll(e){this.iteratePlugins(t=>{t.update(e)})}setState(e){this.store.setState(e)}getState(){return this.store.getState()}patchFilesState(e){const t=this.getState().files;this.setState({files:{...t,...Object.fromEntries(Object.entries(e).map(s=>{let[i,o]=s;return[i,{...t[i],...o}]}))}})}setFileState(e,t){if(!this.getState().files[e])throw new Error(`Can’t set state for ${e} (the file could have been removed)`);this.patchFilesState({[e]:t})}i18nInit(){const e=s=>this.log(`Missing i18n string: ${s}`,"error"),t=new de([this.defaultLocale,this.opts.locale],{onMissingKey:e});this.i18n=t.translate.bind(t),this.i18nArray=t.translateArray.bind(t),this.locale=t.locale}setOptions(e){this.opts={...this.opts,...e,restrictions:{...this.opts.restrictions,...e==null?void 0:e.restrictions}},e.meta&&this.setMeta(e.meta),this.i18nInit(),e.locale&&this.iteratePlugins(t=>{t.setOptions(e)}),this.setState(void 0)}resetProgress(){const e={percentage:0,bytesUploaded:0,uploadComplete:!1,uploadStarted:null},t={...this.getState().files},s={};Object.keys(t).forEach(i=>{s[i]={...t[i],progress:{...t[i].progress,...e}}}),this.setState({files:s,...L}),this.emit("reset-progress")}clearUploadedFiles(){const{capabilities:e,currentUploads:t}=this.getState();if(Object.keys(t).length>0&&!e.individualCancellation)throw new Error("The installed uploader plugin does not allow removing files during an upload.");this.setState({...L,files:{}})}addPreProcessor(e){l(this,T)[T].add(e)}removePreProcessor(e){return l(this,T)[T].delete(e)}addPostProcessor(e){l(this,U)[U].add(e)}removePostProcessor(e){return l(this,U)[U].delete(e)}addUploader(e){l(this,A)[A].add(e)}removeUploader(e){return l(this,A)[A].delete(e)}setMeta(e){const t={...this.getState().meta,...e},s={...this.getState().files};Object.keys(s).forEach(i=>{s[i]={...s[i],meta:{...s[i].meta,...e}}}),this.log("Adding metadata:"),this.log(e),this.setState({meta:t,files:s})}setFileMeta(e,t){const s={...this.getState().files};if(!s[e]){this.log("Was trying to set metadata for a file that has been removed: ",e);return}const i={...s[e].meta,...t};s[e]={...s[e],meta:i},this.setState({files:s})}getFile(e){return this.getState().files[e]}getFiles(){const{files:e}=this.getState();return Object.values(e)}getFilesByIds(e){return e.map(t=>this.getFile(t))}getObjectOfFilesPerState(){const{files:e,totalProgress:t,error:s}=this.getState(),i=Object.values(e),o=[],n=[],a=[],u=[],d=[],g=[],c=[],x=[],O=[];for(const f of i){const{progress:b}=f;!b.uploadComplete&&b.uploadStarted&&(o.push(f),f.isPaused||x.push(f)),b.uploadStarted||n.push(f),(b.uploadStarted||b.preprocess||b.postprocess)&&a.push(f),b.uploadStarted&&u.push(f),f.isPaused&&d.push(f),b.uploadComplete&&g.push(f),f.error&&c.push(f),(b.preprocess||b.postprocess)&&O.push(f)}return{newFiles:n,startedFiles:a,uploadStartedFiles:u,pausedFiles:d,completeFiles:g,erroredFiles:c,inProgressFiles:o,inProgressNotPausedFiles:x,processingFiles:O,isUploadStarted:u.length>0,isAllComplete:t===100&&g.length===i.length&&O.length===0,isAllErrored:!!s&&c.length===i.length,isAllPaused:o.length!==0&&d.length===o.length,isUploadInProgress:o.length>0,isSomeGhost:i.some(f=>f.isGhost)}}validateRestrictions(e,t){t===void 0&&(t=this.getFiles());try{l(this,m)[m].validate(t,[e])}catch(s){return s}return null}checkIfFileAlreadyExists(e){const{files:t}=this.getState();return!!(t[e]&&!t[e].isGhost)}addFile(e){l(this,N)[N](e);const{nextFilesState:t,validFilesToAdd:s,errors:i}=l(this,C)[C]([e]),o=i.filter(a=>a.isRestriction);if(l(this,v)[v](o),i.length>0)throw i[0];this.setState({files:t});const[n]=s;return this.emit("file-added",n),this.emit("files-added",s),this.log(`Added file: ${n.name}, ${n.id}, mime type: ${n.type}`),l(this,M)[M](),n.id}addFiles(e){l(this,N)[N]();const{nextFilesState:t,validFilesToAdd:s,errors:i}=l(this,C)[C](e),o=i.filter(a=>a.isRestriction);l(this,v)[v](o);const n=i.filter(a=>!a.isRestriction);if(n.length>0){let a=`Multiple errors occurred while adding files:
`;if(n.forEach(u=>{a+=`
 * ${u.message}`}),this.info({message:this.i18n("addBulkFilesFailed",{smart_count:n.length}),details:a},"error",this.opts.infoTimeout),typeof AggregateError=="function")throw new AggregateError(n,a);{const u=new Error(a);throw u.errors=n,u}}this.setState({files:t}),s.forEach(a=>{this.emit("file-added",a)}),this.emit("files-added",s),s.length>5?this.log(`Added batch of ${s.length} files`):Object.values(s).forEach(a=>{this.log(`Added file: ${a.name}
 id: ${a.id}
 type: ${a.type}`)}),s.length>0&&l(this,M)[M]()}removeFiles(e,t){const{files:s,currentUploads:i}=this.getState(),o={...s},n={...i},a=Object.create(null);e.forEach(c=>{s[c]&&(a[c]=s[c],delete o[c])});function u(c){return a[c]===void 0}Object.keys(n).forEach(c=>{const x=i[c].fileIDs.filter(u);if(x.length===0){delete n[c];return}const{capabilities:O}=this.getState();if(x.length!==i[c].fileIDs.length&&!O.individualCancellation)throw new Error("The installed uploader plugin does not allow removing files during an upload.");n[c]={...i[c],fileIDs:x}});const d={currentUploads:n,files:o};Object.keys(o).length===0&&(d.allowNewUpload=!0,d.error=null,d.recoveredState=null),this.setState(d),this.calculateTotalProgress();const g=Object.keys(a);g.forEach(c=>{this.emit("file-removed",a[c],t)}),g.length>5?this.log(`Removed ${g.length} files`):this.log(`Removed files: ${g.join(", ")}`)}removeFile(e,t){this.removeFiles([e],t)}pauseResume(e){if(!this.getState().capabilities.resumableUploads||this.getFile(e).progress.uploadComplete)return;const s=!(this.getFile(e).isPaused||!1);return this.setFileState(e,{isPaused:s}),this.emit("upload-pause",e,s),s}pauseAll(){const e={...this.getState().files};Object.keys(e).filter(s=>!e[s].progress.uploadComplete&&e[s].progress.uploadStarted).forEach(s=>{const i={...e[s],isPaused:!0};e[s]=i}),this.setState({files:e}),this.emit("pause-all")}resumeAll(){const e={...this.getState().files};Object.keys(e).filter(s=>!e[s].progress.uploadComplete&&e[s].progress.uploadStarted).forEach(s=>{const i={...e[s],isPaused:!1,error:null};e[s]=i}),this.setState({files:e}),this.emit("resume-all")}retryAll(){const e={...this.getState().files},t=Object.keys(e).filter(i=>e[i].error);if(t.forEach(i=>{const o={...e[i],isPaused:!1,error:null};e[i]=o}),this.setState({files:e,error:null}),this.emit("retry-all",t),t.length===0)return Promise.resolve({successful:[],failed:[]});const s=l(this,S)[S](t,{forceAllowNewUpload:!0});return l(this,P)[P](s)}cancelAll(e){let{reason:t="user"}=e===void 0?{}:e;if(this.emit("cancel-all",{reason:t}),t==="user"){const{files:s}=this.getState(),i=Object.keys(s);i.length&&this.removeFiles(i,"cancel-all"),this.setState(L)}}retryUpload(e){this.setFileState(e,{error:null,isPaused:!1}),this.emit("upload-retry",e);const t=l(this,S)[S]([e],{forceAllowNewUpload:!0});return l(this,P)[P](t)}logout(){this.iteratePlugins(e=>{var t;(t=e.provider)==null||t.logout==null||t.logout()})}calculateTotalProgress(){const t=this.getFiles().filter(d=>d.progress.uploadStarted||d.progress.preprocess||d.progress.postprocess);if(t.length===0){this.emit("progress",0),this.setState({totalProgress:0});return}const s=t.filter(d=>d.progress.bytesTotal!=null),i=t.filter(d=>d.progress.bytesTotal==null);if(s.length===0){const d=t.length*100,g=i.reduce((x,O)=>x+O.progress.percentage,0),c=Math.round(g/d*100);this.setState({totalProgress:c});return}let o=s.reduce((d,g)=>{var c;return d+((c=g.progress.bytesTotal)!=null?c:0)},0);const n=o/s.length;o+=n*i.length;let a=0;s.forEach(d=>{a+=d.progress.bytesUploaded}),i.forEach(d=>{a+=n*(d.progress.percentage||0)/100});let u=o===0?0:Math.round(a/o*100);u>100&&(u=100),this.setState({totalProgress:u}),this.emit("progress",u)}updateOnlineStatus(){var e;((e=window.navigator.onLine)!=null?e:!0)?(this.emit("is-online"),this.wasOffline&&(this.emit("back-online"),this.info(this.i18n("connectedToInternet"),"success",3e3),this.wasOffline=!1)):(this.emit("is-offline"),this.info(this.i18n("noInternetConnection"),"error",0),this.wasOffline=!0)}getID(){return this.opts.id}use(e,t){if(typeof e!="function"){const n=`Expected a plugin class, but got ${e===null?"null":typeof e}. Please verify that the plugin was imported and spelled correctly.`;throw new TypeError(n)}const s=new e(this,t),i=s.id;if(!i)throw new Error("Your plugin must have an id");if(!s.type)throw new Error("Your plugin must have a type");const o=this.getPlugin(i);if(o){const n=`Already found a plugin named '${o.id}'. Tried to use: '${i}'.
Uppy plugins must have unique \`id\` options. See https://uppy.io/docs/plugins/#id.`;throw new Error(n)}return e.VERSION&&this.log(`Using ${i} v${e.VERSION}`),s.type in l(this,p)[p]?l(this,p)[p][s.type].push(s):l(this,p)[p][s.type]=[s],s.install(),this.emit("plugin-added",s),this}getPlugin(e){for(const t of Object.values(l(this,p)[p])){const s=t.find(i=>i.id===e);if(s!=null)return s}}[oe](e){return l(this,p)[p][e]}iteratePlugins(e){Object.values(l(this,p)[p]).flat(1).forEach(e)}removePlugin(e){this.log(`Removing plugin ${e.id}`),this.emit("plugin-remove",e),e.uninstall&&e.uninstall();const t=l(this,p)[p][e.type],s=t.findIndex(n=>n.id===e.id);s!==-1&&t.splice(s,1);const o={plugins:{...this.getState().plugins,[e.id]:void 0}};this.setState(o)}close(e){let{reason:t}=e===void 0?{}:e;this.log(`Closing Uppy instance ${this.opts.id}: removing all files and uninstalling plugins`),this.cancelAll({reason:t}),l(this,j)[j](),this.iteratePlugins(s=>{this.removePlugin(s)}),typeof window<"u"&&window.removeEventListener&&(window.removeEventListener("online",l(this,y)[y]),window.removeEventListener("offline",l(this,y)[y]))}hideInfo(){const{info:e}=this.getState();this.setState({info:e.slice(1)}),this.emit("info-hidden")}info(e,t,s){t===void 0&&(t="info"),s===void 0&&(s=3e3);const i=typeof e=="object";this.setState({info:[...this.getState().info,{type:t,message:i?e.message:e,details:i?e.details:null}]}),setTimeout(()=>this.hideInfo(),s),this.emit("info-visible")}log(e,t){const{logger:s}=this.opts;switch(t){case"error":s.error(e);break;case"warning":s.warn(e);break;default:s.debug(e);break}}registerRequestClient(e,t){l(this,R)[R].set(e,t)}getRequestClientForFile(e){if(!e.remote)throw new Error(`Tried to get RequestClient for a non-remote file ${e.id}`);const t=l(this,R)[R].get(e.remote.requestClientId);if(t==null)throw new Error(`requestClientId "${e.remote.requestClientId}" not registered for file "${e.id}"`);return t}restore(e){return this.log(`Core: attempting to restore upload "${e}"`),this.getState().currentUploads[e]?l(this,P)[P](e):(l(this,$)[$](e),Promise.reject(new Error("Nonexistent upload")))}[ne](){return l(this,S)[S](...arguments)}addResultData(e,t){if(!l(this,X)[X](e)){this.log(`Not setting result for an upload that has been removed: ${e}`);return}const{currentUploads:s}=this.getState(),i={...s[e],result:{...s[e].result,...t}};this.setState({currentUploads:{...s,[e]:i}})}upload(){var e;(e=l(this,p)[p].uploader)!=null&&e.length||this.log("No uploader type plugins are used","warning");let{files:t}=this.getState();const s=this.opts.onBeforeUpload(t);return s===!1?Promise.reject(new Error("Not starting the upload because onBeforeUpload returned false")):(s&&typeof s=="object"&&(t=s,this.setState({files:t})),Promise.resolve().then(()=>l(this,m)[m].validateMinNumberOfFiles(t)).catch(i=>{throw l(this,v)[v]([i]),i}).then(()=>{if(!l(this,V)[V](t))throw new w(this.i18n("missingRequiredMetaField"))}).catch(i=>{throw i}).then(()=>{const{currentUploads:i}=this.getState(),o=Object.values(i).flatMap(u=>u.fileIDs),n=[];Object.keys(t).forEach(u=>{const d=this.getFile(u);!d.progress.uploadStarted&&o.indexOf(u)===-1&&n.push(d.id)});const a=l(this,S)[S](n);return l(this,P)[P](a)}).catch(i=>{throw this.emit("error",i),this.log(i,"error"),i}))}}function Ce(r){for(const o of r)o.isRestriction?this.emit("restriction-failed",o.file,o):this.emit("error",o,o.file),this.log(o,"warning");const e=r.filter(o=>o.isUserFacing),t=4,s=e.slice(0,t),i=e.slice(t);s.forEach(o=>{let{message:n,details:a=""}=o;this.info({message:n,details:a},"error",this.opts.infoTimeout)}),i.length>0&&this.info({message:this.i18n("additionalRestrictionsFailed",{count:i.length})})}function Re(r){const{missingFields:e,error:t}=l(this,m)[m].getMissingRequiredMetaFields(r);return e.length>0?(this.setFileState(r.id,{missingRequiredMetaFields:e}),this.log(t.message),this.emit("restriction-failed",r,t),!1):!0}function ze(r){let e=!0;for(const t of Object.values(r))l(this,I)[I](t)||(e=!1);return e}function Ie(r){const{allowNewUpload:e}=this.getState();if(e===!1){const t=new w(this.i18n("noMoreFilesAllowed"),{file:r});throw l(this,v)[v]([t]),t}}function _e(r){const e=r instanceof File?{name:r.name,type:r.type,size:r.size,data:r}:r,t=ie(e),s=Fe(t,e),i=ee(s).extension,o=ye(e,this.getID()),n=e.meta||{};n.name=s,n.type=t;const a=Number.isFinite(e.data.size)?e.data.size:null;return{source:e.source||"",id:o,name:s,extension:i||"",meta:{...this.getState().meta,...n},type:t,data:e.data,progress:{percentage:0,bytesUploaded:0,bytesTotal:a,uploadComplete:!1,uploadStarted:null},size:a,isGhost:!1,isRemote:e.isRemote||!1,remote:e.remote||"",preview:e.preview}}function Le(){this.opts.autoProceed&&!this.scheduledAutoProceed&&(this.scheduledAutoProceed=setTimeout(()=>{this.scheduledAutoProceed=null,this.upload().catch(r=>{r.isRestriction||this.log(r.stack||r.message||r)})},4))}function qe(r){const{files:e}=this.getState(),t={...e},s=[],i=[];for(const n of r)try{var o;let a=l(this,K)[K](n);const u=(o=e[a.id])==null?void 0:o.isGhost;u&&(a={...e[a.id],isGhost:!1,data:n.data},this.log(`Replaced the blob in the restored ghost file: ${a.name}, ${a.id}`));const d=this.opts.onBeforeFileAdded(a,t);if(!d&&this.checkIfFileAlreadyExists(a.id))throw new w(this.i18n("noDuplicates",{fileName:a.name}),{file:n});if(d===!1&&!u)throw new w("Cannot add the file because onBeforeFileAdded returned false.",{isUserFacing:!1,file:n});typeof d=="object"&&d!==null&&(a=d),l(this,m)[m].validateSingleFile(a),t[a.id]=a,s.push(a)}catch(a){i.push(a)}try{l(this,m)[m].validateAggregateRestrictions(Object.values(e),s)}catch(n){return i.push(n),{nextFilesState:e,validFilesToAdd:[],errors:i}}return{nextFilesState:t,validFilesToAdd:s,errors:i}}function ke(){const r=(s,i,o)=>{let n=s.message||"Unknown error";s.details&&(n+=` ${s.details}`),this.setState({error:n}),i!=null&&i.id in this.getState().files&&this.setFileState(i.id,{error:n,response:o})};this.on("error",r),this.on("upload-error",(s,i,o)=>{if(r(i,s,o),typeof i=="object"&&i.message){var n;this.log(i.message,"error");const a=new Error(this.i18n("failedToUpload",{file:(n=s==null?void 0:s.name)!=null?n:""}));a.isUserFacing=!0,a.details=i.message,i.details&&(a.details+=` ${i.details}`),l(this,v)[v]([a])}else l(this,v)[v]([i])});let e=null;this.on("upload-stalled",(s,i)=>{const{message:o}=s,n=i.map(a=>a.meta.name).join(", ");e||(this.info({message:o,details:n},"warning",this.opts.infoTimeout),e=setTimeout(()=>{e=null},this.opts.infoTimeout)),this.log(`${o} ${n}`.trim(),"warning")}),this.on("upload",()=>{this.setState({error:null})});const t=s=>{const i=s.filter(n=>{const a=n!=null&&this.getFile(n.id);return a||this.log(`Not setting progress for a file that has been removed: ${n==null?void 0:n.id}`),a}),o=Object.fromEntries(i.map(n=>[n.id,{progress:{uploadStarted:Date.now(),uploadComplete:!1,percentage:0,bytesUploaded:0,bytesTotal:n.size}}]));this.patchFilesState(o)};this.on("upload-start",s=>{s.forEach(i=>{this.emit("upload-started",i)}),t(s)}),this.on("upload-progress",this.calculateProgress),this.on("upload-success",(s,i)=>{if(s==null||!this.getFile(s.id)){this.log(`Not setting progress for a file that has been removed: ${s==null?void 0:s.id}`);return}const o=this.getFile(s.id).progress;this.setFileState(s.id,{progress:{...o,postprocess:l(this,U)[U].size>0?{mode:"indeterminate"}:void 0,uploadComplete:!0,percentage:100,bytesUploaded:o.bytesTotal},response:i,uploadURL:i.uploadURL,isPaused:!1}),s.size==null&&this.setFileState(s.id,{size:i.bytesUploaded||o.bytesTotal}),this.calculateTotalProgress()}),this.on("preprocess-progress",(s,i)=>{if(s==null||!this.getFile(s.id)){this.log(`Not setting progress for a file that has been removed: ${s==null?void 0:s.id}`);return}this.setFileState(s.id,{progress:{...this.getFile(s.id).progress,preprocess:i}})}),this.on("preprocess-complete",s=>{if(s==null||!this.getFile(s.id)){this.log(`Not setting progress for a file that has been removed: ${s==null?void 0:s.id}`);return}const i={...this.getState().files};i[s.id]={...i[s.id],progress:{...i[s.id].progress}},delete i[s.id].progress.preprocess,this.setState({files:i})}),this.on("postprocess-progress",(s,i)=>{if(s==null||!this.getFile(s.id)){this.log(`Not setting progress for a file that has been removed: ${s==null?void 0:s.id}`);return}this.setFileState(s.id,{progress:{...this.getState().files[s.id].progress,postprocess:i}})}),this.on("postprocess-complete",s=>{if(s==null||!this.getFile(s.id)){this.log(`Not setting progress for a file that has been removed: ${s==null?void 0:s.id}`);return}const i={...this.getState().files};i[s.id]={...i[s.id],progress:{...i[s.id].progress}},delete i[s.id].progress.postprocess,this.setState({files:i})}),this.on("restored",()=>{this.calculateTotalProgress()}),this.on("dashboard:file-edit-complete",s=>{s&&l(this,I)[I](s)}),typeof window<"u"&&window.addEventListener&&(window.addEventListener("online",l(this,y)[y]),window.addEventListener("offline",l(this,y)[y]),setTimeout(l(this,y)[y],3e3))}function Be(r,e){e===void 0&&(e={});const{forceAllowNewUpload:t=!1}=e,{allowNewUpload:s,currentUploads:i}=this.getState();if(!s&&!t)throw new Error("Cannot create a new upload: already uploading.");const o=ue();return this.emit("upload",{id:o,fileIDs:r}),this.setState({allowNewUpload:this.opts.allowMultipleUploadBatches!==!1&&this.opts.allowMultipleUploads!==!1,currentUploads:{...i,[o]:{fileIDs:r,step:0,result:{}}}}),o}function Ge(r){const{currentUploads:e}=this.getState();return e[r]}function Ye(r){const e={...this.getState().currentUploads};delete e[r],this.setState({currentUploads:e})}async function He(r){const e=()=>{const{currentUploads:o}=this.getState();return o[r]};let t=e();const s=[...l(this,T)[T],...l(this,A)[A],...l(this,U)[U]];try{for(let o=t.step||0;o<s.length&&t;o++){const n=s[o];this.setState({currentUploads:{...this.getState().currentUploads,[r]:{...t,step:o}}});const{fileIDs:a}=t;await n(a,r),t=e()}}catch(o){throw l(this,$)[$](r),o}if(t){t.fileIDs.forEach(u=>{const d=this.getFile(u);d&&d.progress.postprocess&&this.emit("postprocess-complete",d)});const o=t.fileIDs.map(u=>this.getFile(u)),n=o.filter(u=>!u.error),a=o.filter(u=>u.error);this.addResultData(r,{successful:n,failed:a,uploadID:r}),t=e()}let i;return t&&(i=t.result,this.emit("complete",i),l(this,$)[$](r)),i==null&&this.log(`Not setting result for an upload that has been removed: ${r}`),i}G.VERSION=Me.version;const st=G;function Ve(r){return typeof r!="object"||r===null||!("nodeType"in r)?!1:r.nodeType===Node.ELEMENT_NODE}function We(r,e){return e===void 0&&(e=document),typeof r=="string"?e.querySelector(r):Ve(r)?r:null}function Xe(r){for(var e;r&&!r.dir;)r=r.parentNode;return(e=r)==null?void 0:e.dir}function D(r,e){if(!Object.prototype.hasOwnProperty.call(r,e))throw new TypeError("attempted to use private field on non-instance");return r}var Ke=0;function Je(r){return"__private_"+Ke+++"_"+r}function Ze(r){let e=null,t;return function(){for(var s=arguments.length,i=new Array(s),o=0;o<s;o++)i[o]=arguments[o];return t=i,e||(e=Promise.resolve().then(()=>(e=null,r(...t)))),e}}var z=Je("updateUI");class B extends ce{constructor(){super(...arguments),Object.defineProperty(this,z,{writable:!0,value:void 0})}getTargetPlugin(e){let t;if(typeof(e==null?void 0:e.addTarget)=="function")t=e,t instanceof B||console.warn(new Error("The provided plugin is not an instance of UIPlugin. This is an indication of a bug with the way Uppy is bundled.",{cause:{targetPlugin:t,UIPlugin:B}}));else if(typeof e=="function"){const s=e;this.uppy.iteratePlugins(i=>{i instanceof s&&(t=i)})}return t}mount(e,t){const s=t.id,i=We(e);if(i){this.isTargetDOMEl=!0;const a=document.createElement("div");return a.classList.add("uppy-Root"),D(this,z)[z]=Ze(u=>{this.uppy.getPlugin(this.id)&&(J(this.render(u),a),this.afterUpdate())}),this.uppy.log(`Installing ${s} to a DOM element '${e}'`),this.opts.replaceTargetContent&&(i.innerHTML=""),J(this.render(this.uppy.getState()),a),this.el=a,i.appendChild(a),a.dir=this.opts.direction||Xe(a)||"ltr",this.onMount(),this.el}const o=this.getTargetPlugin(e);if(o)return this.uppy.log(`Installing ${s} to ${o.id}`),this.parent=o,this.el=o.addTarget(t),this.onMount(),this.el;this.uppy.log(`Not installing ${s}`);let n=`Invalid target option given to ${s}.`;throw typeof e=="function"?n+=" The given target is not a Plugin class. Please check that you're not specifying a React Component instead of a plugin. If you are using @uppy/* packages directly, make sure you have only 1 version of @uppy/core installed: run `npm ls @uppy/core` on the command line and verify that all the versions match and are deduped correctly.":n+="If you meant to target an HTML element, please make sure that the element exists. Check that the <script> tag initializing Uppy is right before the closing </body> tag at the end of the page. (see https://github.com/transloadit/uppy/issues/1042)\n\nIf you meant to target a plugin, please confirm that your `import` statements or `require` calls are correct.",new Error(n)}render(e){throw new Error("Extend the render method to add your plugin to a DOM element")}update(e){if(this.el!=null){var t,s;(t=(s=D(this,z))[z])==null||t.call(s,e)}}unmount(){if(this.isTargetDOMEl){var e;(e=this.el)==null||e.remove()}this.onUnmount()}onMount(){}onUnmount(){}}const it=B;export{it as U,st as a,Xe as g,Ve as i};
