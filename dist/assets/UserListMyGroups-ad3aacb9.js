import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as M}from"./vendor-851db8c1.js";import{l as fe,a as ge,b as C,c as he,q as xe}from"./index.esm-4466ae65.js";import{G as be,A as ye,C as ve,a as je,E as we,U as Se,I as Me}from"./EditGroupNameModal-b45f8321.js";import{G as Ce,A as Ae,u as Ee,h as Ne,R as A,J as O,T as Re,M as Ie,D as k,H as Te,b as $}from"./index-97f6f167.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-icons-51bc3cff.js";import"./react-hook-form-687afde5.js";import"./yup-2824f222.js";import"./@hookform/resolvers-67648cca.js";import"./yup-5f77b7d2.js";import"./react-phone-input-2-57d1f0dd.js";import"./country-state-city-282f4569.js";/* empty css              */import"./react-tooltip-7a26650a.js";import"./@mantine/core-8cbffb6d.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let _e=new Re,i=new Ie;function Cs(){var G;const[c,q]=r.useState([]),{dispatch:x}=M.useContext(Ce);M.useContext(Ae);const[J,g]=r.useState(!1),[H,E]=r.useState(!1),[K,b]=r.useState(!1),[V,p]=r.useState(!1),[Pe,z]=r.useState(null),[De,N]=r.useState(!1),[y,B]=r.useState([]);r.useState(null);const[X,R]=r.useState(null),[Q,I]=r.useState(!1),[W,T]=r.useState(!1),[Y,v]=r.useState(!1),[Z,_]=r.useState(!1),[t,o]=r.useState(null),[d,j]=r.useState(null),[ee,w]=r.useState(!1),[se,P]=r.useState(!1),[te,D]=r.useState(!1),{club:l,user_profile:f,triggerRefetch:F}=Ee(),[re,h]=r.useState(!1),[Fe,oe]=r.useState([]),[L,ae]=r.useState([]),U=localStorage.getItem("user"),ie=async()=>{const s=localStorage.getItem("user");try{const n=(await _e.getList("user",{filter:["role,cs,user",`club_id,eq,${l==null?void 0:l.id}`]})).list.filter(u=>(u==null?void 0:u.id)!=s);B(n)}catch(a){console.error("Error fetching users:",a)}},le=async()=>{try{const s=await i.callRawAPI("/v3/api/custom/courtmatchup/user/groups/pending-invites",{},"GET");oe(s.pending_invites)}catch(s){console.error("Error fetching pending invites:",s)}},ne=async()=>{try{const s=await i.callRawAPI("/v3/api/custom/courtmatchup/user/groups/sent-invites",{},"GET");console.log("sent invites",s),!s.error&&s.invites&&ae(s.invites)}catch(s){console.error("Error fetching sent invites:",s)}},m=async()=>{E(!0);try{const s=await i.callRawAPI("/v3/api/custom/courtmatchup/user/groups?type=0",{},"GET");q(s.groups)}catch(s){console.error("Error fetching users:",s)}finally{E(!1)}};r.useEffect(()=>{m(),le(),ne()},[F]),r.useEffect(()=>{ie()},[F]),M.useEffect(()=>{x({type:"SETPATH",payload:{path:"my-groups"}})},[]);const me=s=>{z(s),g(!0)},ce=s=>{const a=c.flatMap(n=>n.members).find(n=>n.id===s);a&&(R(a),I(!0))},de=async()=>{var s;P(!0);try{const n=((s=t.members)==null?void 0:s.map(S=>S.id)).filter(S=>S!==d.id),u={id:t.id,members:JSON.stringify(n)};i.setTable("user_groups");const Le=await i.callRestAPI(u,"PUT");i.setTable("activity_logs"),await i.callRestAPI({user_id:U,action:"Removed member from group",activity_type:k.group,action_type:Te.DELETE,data:JSON.stringify(u),club_id:l==null?void 0:l.id,description:"Removed member from group"},"POST"),o(null),j(null),w(!1),$(x,"Member removed successfully"),m()}catch(a){console.error("Error removing member:",a),$(x,a.message,3e3,"error")}finally{P(!1)}},ue=async()=>{_(!0);try{i.setTable("user_groups"),await i.callRestAPI({id:t.group_id},"DELETE"),i.setTable("activity_logs"),await i.callRestAPI({user_id:U,action:"Deleted group",type:k.delete_group,data:JSON.stringify({group:t}),club_id:l==null?void 0:l.id},"POST"),v(!1),o(null),m()}catch(s){console.error("Error deleting group:",s)}finally{_(!1)}},pe=async s=>{N(!0);try{console.log("Adding family member:",s),await new Promise(a=>setTimeout(a,1e3)),p(!1)}catch(a){console.error("Error adding family member:",a)}finally{N(!1)}};return e.jsxs("div",{children:[H&&e.jsx(Ne,{}),e.jsxs("div",{className:"flex items-center justify-between bg-white px-4 py-4",children:[e.jsx("h1",{className:"mb-6 text-2xl font-semibold",children:"My groups"}),e.jsx("button",{onClick:()=>b(!0),className:"rounded-lg bg-primaryBlue px-3 py-2 text-white",children:"Create group"})]}),e.jsxs("div",{className:"mx-auto mt-5 max-w-7xl p-5",children:[e.jsx("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:(c==null?void 0:c.length)>0?c==null?void 0:c.map(s=>e.jsx(be,{group:s,sentInvites:L,onAddExistingUser:()=>{o(s),h(!0)},onAddMember:me,onEditName:()=>{D(!0),o(s)},onDeleteGroup:()=>{v(!0),o(s)},onViewProfile:ce,onRemoveMember:(a,n)=>{w(!0),j(n),o(a)},onAddFamilyMember:()=>{p(!0),o(s)},onInviteToCourtmatch:()=>{T(!0),o(s)}},s.id)):e.jsx("div",{className:"col-span-3",children:e.jsx("p",{className:"text-center text-gray-500",children:"No groups found"})})}),J&&e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-96 rounded-lg bg-white p-6",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Add Member"}),e.jsx("button",{onClick:()=>g(!1),className:"rounded hover:bg-gray-100",children:e.jsx(fe,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("button",{className:"flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50",onClick:()=>{g(!1),h(!0),o(t)},children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ge,{className:"mr-2"}),e.jsx("span",{children:"Add existing user"})]}),e.jsx(C,{})]}),e.jsxs("button",{className:"flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50",onClick:()=>{g(!1),p(!0),o(t)},children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"mr-2"}),e.jsx("span",{children:"Add family member"})]}),e.jsx(C,{})]}),e.jsxs("button",{className:"flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(xe,{className:"mr-2"}),e.jsx("span",{children:"Invite to CourtMatch"})]}),e.jsx(C,{})]})]})]})}),e.jsx(A,{isOpen:V,title:"Add family member",showFooter:!1,onClose:()=>{p(!1),o(null)},children:e.jsx(ye,{users:y,user:f,fetchData:m,group:t,onSubmit:pe,onClose:()=>{p(!1),o(null)}})}),e.jsx(A,{isOpen:K,onClose:()=>b(!1),title:"Create group",showFooter:!1,children:e.jsx(ve,{users:y,fetchData:m,user:f,onClose:()=>b(!1)})}),e.jsx(A,{isOpen:re,onClose:()=>{h(!1),o(null)},showFooter:!1,title:`Add user to ${t==null?void 0:t.group_name}`,children:e.jsx(je,{user:f,users:y,group:t,fetchData:m,sentInvites:L,onClose:()=>{h(!1),o(null)}})}),te&&e.jsx(we,{title:`Edit ${t==null?void 0:t.group_name}`,group:t,user:f,onClose:s=>{D(!1),o(null),s&&m()}}),e.jsx(Se,{isOpen:Q,onClose:()=>{I(!1),R(null)},fetchData:m,user:X}),W&&e.jsx(Me,{user:f,title:"Invite a friend to Court Matchup",onClose:()=>{T(!1),o(null)},group:t}),e.jsx(O,{isOpen:Y,onClose:()=>v(!1),title:"Delete group",requireConfirmation:!0,message:`Are you sure you want to delete ${t==null?void 0:t.group_name} with ${(G=t==null?void 0:t.members)==null?void 0:G.length} members?`,onDelete:ue,buttonText:"Delete",loading:Z}),e.jsx(O,{isOpen:ee,onClose:()=>{w(!1),o(null),j(null)},title:"Remove member",message:`Are you sure you want to remove ${d==null?void 0:d.first_name} ${d==null?void 0:d.last_name} from ${t==null?void 0:t.group_name}?`,onDelete:de,requireConfirmation:!0,buttonText:"Remove",loading:se})]})]})}export{Cs as default};
