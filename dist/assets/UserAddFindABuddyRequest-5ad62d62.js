import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as n,f as Ge,b as $e}from"./vendor-851db8c1.js";import"./BottomDrawer-f3121b82.js";import{M as Pe,T as He,G as Je,u as Me,o as Qe,aD as X,an as Ue,h as Ke,b as C,a5 as Z,d as We,a8 as Ve,aG as ee,D as ze,H as Xe}from"./index-97f6f167.js";import{B as Ze}from"./BackButton-11ba52b2.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{T as et}from"./TimeSlots-9ce7c837.js";import{A as tt}from"./AddPlayers-4c2a1b90.js";import{S as at}from"./SportTypeSelection-cc97819b.js";import{C as st}from"./Calendar-282b3fcf.js";import{B as nt}from"./ReservationSummary-c649f1d8.js";import{f as ot}from"./date-fns-07266b7d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";let b=new Pe,te=new He;const ra=({})=>{var M,Q,U,K,W,V;const[N,ae]=n.useState(null);n.useState(null),n.useState(null);const[u,se]=n.useState(null),[h,F]=n.useState(new Date),[rt,ne]=n.useState(null),[oe,re]=n.useState([]),[ie,le]=n.useState([]),[s,de]=n.useState(null),[_,ce]=n.useState(null),[T,ue]=n.useState(null),[v,it]=n.useState(0),[lt,O]=n.useState(0),[q,dt]=n.useState([]),[E,me]=n.useState([]),{state:ct,dispatch:D}=n.useContext(Je),[pe,A]=n.useState(!1),[ut,ge]=n.useState({from:null,until:null}),[w,ye]=n.useState([{from:null,until:null}]),[fe,xe]=n.useState(""),[l,L]=n.useState([]),[he,ve]=n.useState(!1),[k,R]=n.useState(1),[Se,I]=n.useState(!1),[Y,be]=n.useState(""),[G,we]=n.useState(1),[$,je]=n.useState(3.5),[P,Ne]=n.useState(3.5),[_e,mt]=n.useState(null),[Te,pt]=n.useState(!1),[m,B]=n.useState(1),{user_subscription:c,user_permissions:f,club_membership:S}=Me(),a=n.useMemo(()=>!(c!=null&&c.planId)||!(S!=null&&S.length)?null:S.find(e=>e.plan_id===c.planId),[c,S]),j=n.useMemo(()=>{var i,d;if(((i=a==null?void 0:a.advance_booking_enabled)==null?void 0:i.buddy)===!1){const x=new Date;return x.setFullYear(x.getFullYear()+10),x}const e=((d=a==null?void 0:a.advance_booking_days)==null?void 0:d.buddy)||10,o=new Date,r=new Date;return r.setDate(o.getDate()+e),r},[a]),[p,g]=n.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),H=Ge(),J=localStorage.getItem("user"),De=async()=>{try{const e=await te.getOne("user",J,{}),o=await b.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${e.model.club_id}`,{},"GET");me(o.sports),de(o.model)}catch(e){console.error(e)}},ke=async()=>{try{const e=await te.getList("user",{filter:["role,cs,user"]});re(e.list)}catch(e){console.error(e)}},Re=async()=>{try{const e=await b.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");le(e.groups)}catch(e){console.error(e)}};n.useEffect(()=>{(async()=>(I(!0),await De(),await ke(),await Re(),I(!1)))()},[]),$e.useEffect(()=>{Qe({path:"/user/create-request",clubName:s==null?void 0:s.name,favicon:s==null?void 0:s.club_logo,description:"Create Request"})},[s==null?void 0:s.club_logo]);const Be=()=>{F(new Date(h.setMonth(h.getMonth()-1)))},Ce=()=>{F(new Date(h.setMonth(h.getMonth()+1)))},Fe=async()=>{var r,i;if(!(c!=null&&c.planId)){g({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to use the Find a Buddy feature",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(f!=null&&f.allowBuddy)){g({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${f==null?void 0:f.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(u>j&&((r=a==null?void 0:a.advance_booking_enabled)==null?void 0:r.buddy)!==!1){const d=((i=a==null?void 0:a.advance_booking_days)==null?void 0:i.buddy)||10;g({isOpen:!0,title:"Date Selection Error",message:`Your membership plan only allows creating buddy requests ${d} days in advance. Please select a valid date.`,type:"warning"});return}if(!N||!u||!w.length||!_||!T){g({isOpen:!0,title:"Incomplete Details",message:"Please fill in all required fields",type:"warning"});return}const{start_time:e,end_time:o}=Ve(w);A(!0);try{const d=new Date(u),x=w.filter(y=>y.from&&y.until).map(y=>({start_time:ee(y.from),end_time:ee(y.until)}));if(x.length===0){g({isOpen:!0,title:"Time Slots Required",message:"Please select at least one valid time slot",type:"warning"});return}const z={sport_id:N,slots:x,ntrp:$,max_ntrp:P,num_players:G,num_needed:k,type:_,sub_type:T,need_coach:1,notes:Y,date:ot(d,"yyyy-MM-dd"),start_time:e,end_time:o,player_ids:l.map(y=>y.id)},Ye=await b.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/create-request",z,"POST");b.setTable("activity_logs");const gt=await b.callRestAPI({activity_type:ze.find_a_buddy,user_id:J,club_id:s==null?void 0:s.id,action_type:Xe.CREATE,data:JSON.stringify(z),description:"Created a find a buddy request"},"POST");Ye.error||(C(D,"Request created successfully",3e3,"success"),H("/user/find-a-buddy"))}catch(d){console.error(d),g({isOpen:!0,title:"Request Error",message:d.message||"Error creating buddy request",type:"error"})}finally{A(!1)}},Oe=e=>{ge(e),ne(e.from)},qe=e=>{ye(e)};n.useEffect(()=>{if(v&&(l!=null&&l.length)){const e=v*(l==null?void 0:l.length),o=X(s==null?void 0:s.fee_settings,e),r=(s==null?void 0:s.club_fee)||0;O(e+o+r)}else{const e=X(s==null?void 0:s.fee_settings,v),o=(s==null?void 0:s.club_fee)||0;O(v+e+o)}},[v,l,s==null?void 0:s.fee_settings,s==null?void 0:s.club_fee]);const Ee=()=>{R(e=>Math.min(e+1,10))},Ae=()=>{R(e=>Math.max(e-1,0))},Le=()=>{B(2)},Ie=e=>{L(o=>o.some(i=>i.id===e.id)?o.filter(i=>i.id!==e.id):[...o,e])};return t.jsxs("div",{className:"",children:[t.jsx(Ue,{isOpen:p.isOpen,onClose:()=>g({...p,isOpen:!1}),title:p.title,message:p.message,actionButtonText:p.actionButtonText,actionButtonLink:p.actionButtonLink,type:p.type}),Se&&t.jsx(Ke,{}),t.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[m===1&&t.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),m===2&&t.jsx("div",{className:" ",children:"Step 2 • Reserving details"}),m===3&&t.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),t.jsxs("div",{className:"p-4",children:[t.jsx(Ze,{onBack:()=>{m===1?H(-1):B(m===2?1:2)}}),m===1&&t.jsx("div",{children:t.jsx("div",{className:" p-4",children:t.jsx("div",{className:"space-y-6",children:t.jsx("div",{className:"mx-auto max-w-7xl p-4",children:t.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[t.jsx(at,{sports:E,onSelectionChange:({sport:e,type:o,subType:r})=>{ae(e),ce(o),ue(r)}}),t.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[((M=a==null?void 0:a.advance_booking_enabled)==null?void 0:M.buddy)===!1?t.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can create a find-a-buddy request for any future date."}):((Q=a==null?void 0:a.advance_booking_days)==null?void 0:Q.buddy)!==void 0&&t.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can create a find-a-buddy request up to"," ",(U=a==null?void 0:a.advance_booking_days)==null?void 0:U.buddy," ",((K=a==null?void 0:a.advance_booking_days)==null?void 0:K.buddy)===1?"day":"days"," ","in advance."]}),t.jsx(st,{currentMonth:h,selectedDate:u,onDateSelect:e=>{var o,r;if(e>j&&((o=a==null?void 0:a.advance_booking_enabled)==null?void 0:o.buddy)!==!1){const i=((r=a==null?void 0:a.advance_booking_days)==null?void 0:r.buddy)||10;C(D,`Your membership plan only allows booking ${i} days in advance`,3e3,"warning");return}se(e)},onPreviousMonth:Be,onNextMonth:Ce,daysOff:s!=null&&s.days_off?JSON.parse(s.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:j,disabledDateMessage:((W=a==null?void 0:a.advance_booking_enabled)==null?void 0:W.buddy)===!1?"You can book for any future date":`Your membership plan only allows booking ${((V=a==null?void 0:a.advance_booking_days)==null?void 0:V.buddy)||10} days in advance`})]}),t.jsx(et,{isLoading:Te,selectedDate:u,timeRange:q,onTimeClick:Oe,onTimeSlotsChange:qe,onNext:()=>{var e,o;if(u>j&&((e=a==null?void 0:a.advance_booking_enabled)==null?void 0:e.buddy)!==!1){const r=((o=a==null?void 0:a.advance_booking_days)==null?void 0:o.buddy)||10;C(D,`Your membership plan only allows booking ${r} days in advance`,3e3,"warning");return}Le()},nextButtonText:"Next: Players",startHour:0,clubTimes:s!=null&&s.times?JSON.parse(s.times):[],endHour:24,interval:30,isTimeSlotAvailable:()=>!0,multipleSlots:!0})]})})})})}),m===2&&t.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[t.jsx(nt,{selectedSport:N,sports:E,selectedType:_,selectedSubType:T,selectedDate:u,selectedTimes:q,timeSlots:w}),t.jsx(tt,{searchQuery:fe,setSearchQuery:xe,selectedPlayers:l,setSelectedPlayers:L,players:oe,groups:ie,selectedGroup:_e,isFindBuddyEnabled:he,setIsFindBuddyEnabled:ve,playersNeeded:k,handleIncrement:Ee,handleDecrement:Ae,onPlayerToggle:Ie,showAddReservationToFindBuddy:!1,showPlayersNeeded:!1}),t.jsxs("div",{className:"h-fit rounded-xl bg-white shadow-5",children:[t.jsx("div",{className:"rounded-xl bg-gray-50 p-4 text-center",children:t.jsx("h2",{className:"text-base font-medium",children:"Other details"})}),t.jsx("div",{className:"p-4",children:t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"mb-2 text-base font-medium",children:"My group NTRP score"}),t.jsxs("div",{className:"flex gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"relative overflow-hidden rounded-xl border border-gray-200 bg-white",children:[t.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",children:"Min"}),t.jsx("select",{value:$,onChange:e=>je(e.target.value),className:"w-full appearance-none rounded-lg border-0 py-3 pl-12 pr-8 text-right focus:ring-0",children:Z.map(e=>t.jsx("option",{value:e,children:e.toFixed(1)},e))})]})}),t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"relative overflow-hidden rounded-xl border border-gray-200 bg-white",children:[t.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",children:"Max"}),t.jsx("select",{value:P,onChange:e=>Ne(e.target.value),className:"w-full appearance-none rounded-lg border-0 py-3 pl-12 pr-8 text-right focus:ring-0",children:Z.map(e=>t.jsx("option",{value:e,children:e.toFixed(1)},e))})]})})]})]}),t.jsxs("div",{className:"mb-2 flex items-center justify-between gap-2",children:[t.jsx("h3",{className:" text-base font-medium",children:"Players playing"}),t.jsx("div",{className:"",children:t.jsx("select",{value:G,onChange:e=>we(e.target.value),className:"w-fit appearance-none rounded-xl border border-gray-200 bg-white py-2 pl-3 pr-8",children:[...Array(10)].map((e,o)=>t.jsx("option",{value:o+1,children:o+1},o))})})]}),t.jsxs("div",{className:"mb-2 flex items-center justify-between gap-2",children:[t.jsx("h3",{className:" text-base font-medium",children:"Players needed"}),t.jsx("div",{className:"relative",children:t.jsx("select",{value:k,onChange:e=>R(e.target.value),className:"w-full appearance-none rounded-xl border border-gray-200 bg-white py-2 pl-3 pr-8",children:[...Array(10)].map((e,o)=>t.jsx("option",{value:o+1,children:o+1},o))})})]}),t.jsxs("div",{children:[t.jsxs("h3",{className:"mb-2 text-base font-medium",children:["Short bio"," ",t.jsx("span",{className:"text-gray-400",children:"(Optional)"})]}),t.jsx("textarea",{className:"w-full rounded-xl border border-gray-200 p-3 focus:border-gray-200 focus:ring-0",rows:"3",value:Y,onChange:e=>be(e.target.value)})]}),t.jsx(We,{loading:pe,onClick:Fe,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Submit request"})]})})]})]})]})]})};export{ra as default};
