import { lazy } from "react";

export const AddAdminCmsPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminCmsPage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminEmailPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminEmailPage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminPhotoPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminPhotoPage");
  __import.finally(() => {});
  return __import;
});

export const AdminCmsListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminCmsListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEmailListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminEmailListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripePricesListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminStripePricesListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeSubscriptionsListPage = lazy(() => {
  const __import = import(
    "../pages/Admin/List/AdminStripeSubscriptionsListPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminStripeInvoicesListPageV2 = lazy(() => {
  const __import = import("../pages/Admin/List/AdminStripeInvoicesListPageV2");
  __import.finally(() => {});
  return __import;
});

export const AdminPhotoListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminPhotoListPage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminCmsPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/EditAdminCmsPage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminEmailPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/EditAdminEmailPage");
  __import.finally(() => {});
  return __import;
});

export const UserMagicLoginPage = lazy(() => {
  const __import = import("../pages/MagicLogin/UserMagicLoginPage");
  __import.finally(() => {});
  return __import;
});

export const MagicLoginVerifyPage = lazy(() => {
  const __import = import("../pages/MagicLogin/MagicLoginVerifyPage");
  __import.finally(() => {});
  return __import;
});

export const CustomAdminLoginPage = lazy(() => {
  const __import = import("../pages/Admin/Auth/CustomAdminLoginPage");
  __import.finally(() => {});
  return __import;
});

export const CustomAdminSignUpPage = lazy(() => {
  const __import = import("../pages/Admin/Auth/CustomAdminSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const CustomAdminProfilePage = lazy(() => {
  const __import = import("../pages/Admin/View/CustomAdminProfilePage");
  __import.finally(() => {});
  return __import;
});

export const CustomCoachLoginPage = lazy(() => {
  const __import = import("../pages/Coach/Auth/CustomCoachLoginPage");
  __import.finally(() => {});
  return __import;
});

export const CustomCoachSignUpPage = lazy(() => {
  const __import = import("../pages/Coach/Auth/CustomCoachSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const CustomCoachProfilePage = lazy(() => {
  const __import = import("../pages/Coach/View/CustomCoachProfilePage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserLoginPage = lazy(() => {
  const __import = import("../pages/User/Auth/CustomUserLoginPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserSignUpPage = lazy(() => {
  const __import = import("../pages/User/Auth/CustomUserSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const CustomUserProfilePage = lazy(() => {
  const __import = import("../pages/User/View/CustomUserProfilePage");
  __import.finally(() => {});
  return __import;
});

export const CustomStaffLoginPage = lazy(() => {
  const __import = import("../pages/Staff/Auth/CustomStaffLoginPage");
  __import.finally(() => {});
  return __import;
});

export const CustomStaffSignUpPage = lazy(() => {
  const __import = import("../pages/Staff/Auth/CustomStaffSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const CustomStaffProfilePage = lazy(() => {
  const __import = import("../pages/Staff/View/CustomStaffProfilePage");
  __import.finally(() => {});
  return __import;
});

export const CustomClubLoginPage = lazy(() => {
  const __import = import("../pages/Club/Auth/CustomClubLoginPage");
  __import.finally(() => {});
  return __import;
});

export const CustomClubSignUpPage = lazy(() => {
  const __import = import("../pages/Club/Auth/CustomClubSignUpPage");
  __import.finally(() => {});
  return __import;
});

export const CustomClubProfilePage = lazy(() => {
  const __import = import("../pages/Club/View/CustomClubProfilePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListCoachTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListCoachTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubAddCoachTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddCoachTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubEditCoachTablePage = lazy(() => {
  const __import = import("../pages/Club/Edit/ClubEditCoachTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubViewCoachTablePage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewCoachTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListClinicBookingsTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListClinicBookingsTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubAddClinicBookingsTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddClinicBookingsTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubEditClinicBookingsTablePage = lazy(() => {
  const __import = import("../pages/Club/Edit/ClubEditClinicBookingsTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubViewClinicBookingsTablePage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewClinicBookingsTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListStripeInvoiceTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListStripeInvoiceTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubAddStripeInvoiceTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddStripeInvoiceTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubEditStripeInvoiceTablePage = lazy(() => {
  const __import = import("../pages/Club/Edit/ClubEditStripeInvoiceTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubViewStripeInvoiceTablePage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewStripeInvoiceTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListStaffTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListStaffTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubAddStaffTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddStaffTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubEditStaffTablePage = lazy(() => {
  const __import = import("../pages/Club/Edit/ClubEditStaffTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubViewStaffTablePage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewStaffTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListReservationTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListReservationTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubAddReservationTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddReservationTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubEditReservationTablePage = lazy(() => {
  const __import = import("../pages/Club/Edit/ClubEditReservationTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubViewReservationTablePage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewReservationTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListEmailTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListEmailTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubAddEmailTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddEmailTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubEditEmailTablePage = lazy(() => {
  const __import = import("../pages/Club/Edit/ClubEditEmailTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubViewEmailTablePage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewEmailTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListFindABuddyRequestsTablePage = lazy(() => {
  const __import = import(
    "../pages/Club/List/ClubListFindABuddyRequestsTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const ClubAddFindABuddyRequestsTablePage = lazy(() => {
  const __import = import(
    "../pages/Club/Add/ClubAddFindABuddyRequestsTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const ClubEditFindABuddyRequestsTablePage = lazy(() => {
  const __import = import(
    "../pages/Club/Edit/ClubEditFindABuddyRequestsTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const ClubViewFindABuddyRequestsTablePage = lazy(() => {
  const __import = import(
    "../pages/Club/View/ClubViewFindABuddyRequestsTablePage"
  );
  __import.finally(() => {});
  return __import;
});

export const ClubListBuddyTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListBuddyTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubAddBuddyTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddBuddyTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubEditBuddyTablePage = lazy(() => {
  const __import = import("../pages/Club/Edit/ClubEditBuddyTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubViewBuddyTablePage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewBuddyTablePage");
  __import.finally(() => {});
  return __import;
});

export const CoachViewPagesTablePage = lazy(() => {
  const __import = import("../pages/Coach/View/CoachViewPagesTablePage");
  __import.finally(() => {});
  return __import;
});

export const CoachListReservationTablePage = lazy(() => {
  const __import = import("../pages/Coach/List/CoachListReservationTablePage");
  __import.finally(() => {});
  return __import;
});

export const UserListPagesTablePage = lazy(() => {
  const __import = import("../pages/User/List/UserListPagesTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubForgotPage = lazy(() => {
  const __import = import("../pages/Club/Auth/ClubForgotPage");
  __import.finally(() => {});
  return __import;
});

export const ClubResetPage = lazy(() => {
  const __import = import("../pages/Club/Auth/ClubResetPage");
  __import.finally(() => {});
  return __import;
});

export const ClubDashboardPage = lazy(() => {
  const __import = import("../pages/Club/View/ClubDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const StaffForgotPage = lazy(() => {
  const __import = import("../pages/Staff/Auth/StaffForgotPage");
  __import.finally(() => {});
  return __import;
});

export const StaffResetPage = lazy(() => {
  const __import = import("../pages/Staff/Auth/StaffResetPage");
  __import.finally(() => {});
  return __import;
});

export const StaffDashboardPage = lazy(() => {
  const __import = import("../pages/Staff/View/StaffDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const UserForgotPage = lazy(() => {
  const __import = import("../pages/User/Auth/UserForgotPage");
  __import.finally(() => {});
  return __import;
});

export const UserResetPage = lazy(() => {
  const __import = import("../pages/User/Auth/UserResetPage");
  __import.finally(() => {});
  return __import;
});

export const UserDashboardPage = lazy(() => {
  const __import = import("../pages/User/View/UserDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const CoachForgotPage = lazy(() => {
  const __import = import("../pages/Coach/Auth/CoachForgotPage");
  __import.finally(() => {});
  return __import;
});

export const CoachResetPage = lazy(() => {
  const __import = import("../pages/Coach/Auth/CoachResetPage");
  __import.finally(() => {});
  return __import;
});

export const CoachDashboardPage = lazy(() => {
  const __import = import("../pages/Coach/View/CoachDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminStripePricePage = lazy(() => {
  const __import = import("../pages/stripe/AddAdminStripePricePage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminStripeProductPage = lazy(() => {
  const __import = import("../pages/stripe/AddAdminStripeProductPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeChargesListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripeChargesListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeInvoicesListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripeInvoicesListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeOrdersListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripeOrdersListPage");
  __import.finally(() => {});
  return __import;
});

export const AdminStripeProductsListPage = lazy(() => {
  const __import = import("../pages/stripe/AdminStripeProductsListPage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminStripePricePage = lazy(() => {
  const __import = import("../pages/stripe/EditAdminStripePricePage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminStripeProductPage = lazy(() => {
  const __import = import("../pages/stripe/EditAdminStripeProductPage");
  __import.finally(() => {});
  return __import;
});

export const AdminForgotPage = lazy(() => {
  const __import = import("../pages/Admin/Auth/AdminForgotPage");
  __import.finally(() => {});
  return __import;
});

export const AdminResetPage = lazy(() => {
  const __import = import("../pages/Admin/Auth/AdminResetPage");
  __import.finally(() => {});
  return __import;
});

export const AdminDashboardPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const AdminUserListPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminUserListPage");
  __import.finally(() => {});
  return __import;
});

export const AddAdminUserPage = lazy(() => {
  const __import = import("../pages/Admin/Add/AddAdminUserPage");
  __import.finally(() => {});
  return __import;
});

export const EditAdminUserPage = lazy(() => {
  const __import = import("../pages/Admin/Edit/EditAdminUserPage");
  __import.finally(() => {});
  return __import;
});

export const ProfileSetUpPage = lazy(() => {
  const __import = import("../pages/Club/Auth/ProfileSetUp");
  __import.finally(() => {});
  return __import;
});

export const ClubListDailyScheduler = lazy(() => {
  const __import = import("../pages/Club/List/ClubListDailyScheduler");
  __import.finally(() => {});
  return __import;
});

export const ClubCourtManagement = lazy(() => {
  const __import = import("../pages/Club/View/ClubCourtManagement");
  __import.finally(() => {});
  return __import;
});

export const ClubListAvailability = lazy(() => {
  const __import = import("../pages/Club/List/ClubListAvailability");
  __import.finally(() => {});
  return __import;
});

export const ClubListUI = lazy(() => {
  const __import = import("../pages/Club/List/ClubListUI");
  __import.finally(() => {});
  return __import;
});

export const ClubListUsers = lazy(() => {
  const __import = import("../pages/Club/List/ClubListUsersTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubViewUserPage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewUserTablePage");
  __import.finally(() => {});
  return __import;
});
export const ClubListClinicsTable = lazy(() => {
  const __import = import("../pages/Club/List/ClubListClinics");
  __import.finally(() => {});
  return __import;
});

export const ClubAddClinicsTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddClinicsTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListFindABuddyTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListFindABuddyTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListHistoryTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListHistoryTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListInvoiceTablePage = lazy(() => {
  const __import = import("../pages/Club/List/ClubListInvoiceTablePage");
  __import.finally(() => {});
  return __import;
});

export const ClubListCustomRequests = lazy(() => {
  const __import = import("../pages/Club/List/ClubListCustomRequests");
  __import.finally(() => {});
  return __import;
});

export const ClubListLessons = lazy(() => {
  const __import = import("../pages/Club/List/ClubListLessons");
  __import.finally(() => {});
  return __import;
});

export const ClubVerifyEmail = lazy(() => {
  const __import = import("../pages/Club/Auth/ClubVerifyEmail");
  __import.finally(() => {});
  return __import;
});

export const ClubMembership = lazy(() => {
  const __import = import("../pages/Club/List/ClubMembership");
  __import.finally(() => {});
  return __import;
});
export const ClubListPricing = lazy(() => {
  const __import = import("../pages/Club/List/ClubListPricing");
  __import.finally(() => {});
  return __import;
});

export const ClubViewCoachPage = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewCoachPage");
  __import.finally(() => {});
  return __import;
});
export const AdminCustomerSupportPage = lazy(() => {
  const __import = import("../pages/Admin/View/AdminCustomerSupportPage");
  __import.finally(() => {});
  return __import;
});

export const AdminListDailyScheduler = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListDailyScheduler");
  __import.finally(() => {});
  return __import;
});

export const AdminListCourtManagement = lazy(() => {
  const __import = import("../pages/Admin/List/AdminCourtManagement");
  __import.finally(() => {});
  return __import;
});

export const AdminListAvailability = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListAvailability");
  __import.finally(() => {});
  return __import;
});

export const AdminListUI = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListClubUI");
  __import.finally(() => {});
  return __import;
});

export const AdminListcoaches = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListCoaches");
  __import.finally(() => {});
  return __import;
});

export const AdminListStaff = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListStaff");
  __import.finally(() => {});
  return __import;
});

export const AdminListReservation = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListReservation");
  __import.finally(() => {});
  return __import;
});
export const AdminListLessons = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListLessons");
  __import.finally(() => {});
  return __import;
});
export const AdminListProgramsClinics = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListProgramsClinics");
  __import.finally(() => {});
  return __import;
});
export const AdminAddClinicsTablePage = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminAddClinics");
  __import.finally(() => {});
  return __import;
});
export const ClubAddLessonTablePage = lazy(() => {
  const __import = import("../pages/Club/Add/ClubAddLessonsTablePage");
  __import.finally(() => {});
  return __import;
});
export const AdminListEmail = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListEmail");
  __import.finally(() => {});
  return __import;
});
export const AdminListCustomRequests = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListCustomRequests");
  __import.finally(() => {});
  return __import;
});
export const AdminListFindABuddy = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListFindABuddy");
  __import.finally(() => {});
  return __import;
});
export const AdminAddFindABuddy = lazy(() => {
  const __import = import("../pages/Admin/Add/AdminFindABuddy");
  __import.finally(() => {});
  return __import;
});
export const AdminListInvoicing = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListInvoicing");
  __import.finally(() => {});
  return __import;
});
export const AdminClubPricing = lazy(() => {
  const __import = import("../pages/Admin/List/AdminClubPricing");
  __import.finally(() => {});
  return __import;
});
export const AdminListMembershipModules = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListMembershipModules");
  __import.finally(() => {});
  return __import;
});

export const PrivacyPolicy = lazy(() => {
  const __import = import("../pages/Public/PrivacyPolicy");
  __import.finally(() => {});
  return __import;
});

export const TermsAndConditions = lazy(() => {
  const __import = import("../pages/Public/TermsAndConditions");
  __import.finally(() => {});
  return __import;
});

export const AdminFeesPage = lazy(() => {
  const __import = import("../pages/Admin/List/AdminFeesPage");
  __import.finally(() => {});
  return __import;
});

export const ClubLandingPage = lazy(() => {
  const __import = import("../pages/Public/ClubLandingPage");
  __import.finally(() => {});
  return __import;
});

export const OAuth = lazy(() => {
  const __import = import("../pages/User/Auth/OAuth");
  __import.finally(() => {});
  return __import;
});

export const CustomSignUpForm = lazy(() => {
  const __import = import("../pages/Club/Edit/ClubCustomSignUpForm");
  __import.finally(() => {});
  return __import;
});

export const StaffCustomSignUpForm = lazy(() => {
  const __import = import("../pages/Staff/View/StaffCustomSignUpForm");
  __import.finally(() => {});
  return __import;
});

export const UserReserveCourt = lazy(() => {
  const __import = import("../pages/User/Add/UserReserveCourt");
  __import.finally(() => {});
  return __import;
});

export const UserLessons = lazy(() => {
  const __import = import("../pages/User/Add/UserLessons");
  __import.finally(() => {});
  return __import;
});
export const UserProgramsClinics = lazy(() => {
  const __import = import("../pages/User/List/UserProgramsClinics");
  __import.finally(() => {});
  return __import;
});

export const UserListMyGroups = lazy(() => {
  const __import = import("../pages/User/List/UserListMyGroups");
  __import.finally(() => {});
  return __import;
});

export const UserAddFindABuddyRequest = lazy(() => {
  const __import = import("../pages/User/Add/UserAddFindABuddyRequest");
  __import.finally(() => {});
  return __import;
});

export const UserMyReservation = lazy(() => {
  const __import = import("../pages/User/List/UserMyReservation");
  __import.finally(() => {});
  return __import;
});

export const UserAcceptInvite = lazy(() => {
  const __import = import("../pages/User/Edit/UserAcceptInvite");
  __import.finally(() => {});
  return __import;
});

export const UserMembership = lazy(() => {
  const __import = import("../pages/User/List/UserListMembership");
  __import.finally(() => {});
  return __import;
});

export const UserClubCalendar = lazy(() => {
  const __import = import("../pages/User/List/UserClubCalendar");
  __import.finally(() => {});
  return __import;
});
export const UserClinicBooking = lazy(() => {
  const __import = import("../pages/User/Add/UserClinicBooking");
  __import.finally(() => {});
  return __import;
});

export const UserListFindABuddy = lazy(() => {
  const __import = import("../pages/User/List/UserListFindABuddy");
  __import.finally(() => {});
  return __import;
});

export const UserCreateRequest = lazy(() => {
  const __import = import("../pages/User/Add/UserCreateRequest");
  __import.finally(() => {});
  return __import;
});

export const UserAddCustomRequest = lazy(() => {
  const __import = import("../pages/User/Add/UserAddCustomRequest");
  __import.finally(() => {});
  return __import;
});

export const PaymentSuccessConfirmation = lazy(() => {
  const __import = import("../pages/User/View/PaymentSuccessConfirmation");
  __import.finally(() => {});
  return __import;
});

export const PaymentReceipt = lazy(() => {
  const __import = import("../pages/User/View/PaymentReceipt");
  __import.finally(() => {});
  return __import;
});

export const CoachProfileSetup = lazy(() => {
  const __import = import("../pages/Coach/Auth/CoachProfileSetup");
  __import.finally(() => {});
  return __import;
});

export const CoachVerifyEmail = lazy(() => {
  const __import = import("../pages/Coach/Auth/CoachVerifyEmail");
  __import.finally(() => {});
  return __import;
});

export const CoachListAvailability = lazy(() => {
  const __import = import("../pages/Coach/List/CoachListAvailability");
  __import.finally(() => {});
  return __import;
});

export const CoachListStatistics = lazy(() => {
  const __import = import("../pages/Coach/List/CoachListStatistics");
  __import.finally(() => {});
  return __import;
});

export const ReservationPayment = lazy(() => {
  const __import = import("../pages/User/Payment/ReservationPayment");
  __import.finally(() => {});
  return __import;
});

export const StaffClubUI = lazy(() => {
  const __import = import("../pages/Staff/View/StaffClubUI");
  __import.finally(() => {});
  return __import;
});

export const StaffAvailability = lazy(() => {
  const __import = import("../pages/Staff/View/StaffAvailability");
  __import.finally(() => {});
  return __import;
});

export const StaffCourtManagement = lazy(() => {
  const __import = import("../pages/Staff/View/StaffCourtManagement");
  __import.finally(() => {});
  return __import;
});

export const StaffDailyScheduler = lazy(() => {
  const __import = import("../pages/Staff/View/StaffDailyScheduler");
  __import.finally(() => {});
  return __import;
});

export const StaffVerifyEmail = lazy(() => {
  const __import = import("../pages/Staff/Auth/StaffVerifyEmail");
  __import.finally(() => {});
  return __import;
});

export const StaffListUsers = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListUsers");
  __import.finally(() => {});
  return __import;
});

export const StaffListCoaches = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListCoaches");
  __import.finally(() => {});
  return __import;
});

export const StaffListStaff = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListStaff");
  __import.finally(() => {});
  return __import;
});

export const StaffViewCoach = lazy(() => {
  const __import = import("../pages/Staff/View/StaffViewCoach");
  __import.finally(() => {});
  return __import;
});

export const ClubViewStaff = lazy(() => {
  const __import = import("../pages/Club/View/ClubViewStaff");
  __import.finally(() => {});
  return __import;
});

export const StaffViewStaff = lazy(() => {
  const __import = import("../pages/Staff/View/StaffViewStaff");
  __import.finally(() => {});
  return __import;
});
export const StaffListReservations = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListReservations");
  __import.finally(() => {});
  return __import;
});

export const StaffListLessons = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListLessons");
  __import.finally(() => {});
  return __import;
});

export const StaffListClinics = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListClinics");
  __import.finally(() => {});
  return __import;
});

export const StaffListFindABuddy = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListFindABuddy");
  __import.finally(() => {});
  return __import;
});

export const StaffListCustomRequest = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListCustomRequest");
  __import.finally(() => {});
  return __import;
});

export const StaffListInvoice = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListInvoice");
  __import.finally(() => {});
  return __import;
});

export const StaffListHistory = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListHistory");
  __import.finally(() => {});
  return __import;
});

export const StaffListEmail = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListEmail");
  __import.finally(() => {});
  return __import;
});

export const StaffListPricing = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListPricing");
  __import.finally(() => {});
  return __import;
});

export const AdminViewStaff = lazy(() => {
  const __import = import("../pages/Admin/View/AdminViewStaff");
  __import.finally(() => {});
  return __import;
});

export const StaffProfileSetup = lazy(() => {
  const __import = import("../pages/Staff/Auth/StaffProfileSetup");
  __import.finally(() => {});
  return __import;
});

export const ClubListFaqs = lazy(() => {
  const __import = import("../pages/Club/List/ClubListFaqs");
  __import.finally(() => {});
  return __import;
});

export const StaffListFaqs = lazy(() => {
  const __import = import("../pages/Staff/List/StaffListFaq");
  __import.finally(() => {});
  return __import;
});

// export const AdminListFaqs = lazy(() => {
//   const __import = import("../pages/Admin/List/AdminListFaqs");
//   __import.finally(() => {});
//   return __import;
// });

export const ClubCustomerSupportPage = lazy(() => {
  const __import = import("../pages/Club/List/ClubCustomerSupportPage");
  __import.finally(() => {});
  return __import;
});

export const AdminListStaffs = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListStaffs");
  __import.finally(() => {});
  return __import;
});

export const AdminListGeneralFaqs = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListGeneralFaqs");
  __import.finally(() => {});
  return __import;
});

export const AdminStaffForgotPage = lazy(() => {
  const __import = import("../pages/AdminStaff/Auth/AdminStaffForgotPage");
  __import.finally(() => {});
  return __import;
});
export const AdminStaffResetPage = lazy(() => {
  const __import = import("../pages/AdminStaff/Auth/AdminStaffResetPage");
  __import.finally(() => {});
  return __import;
});
export const CustomAdminStaffLoginPage = lazy(() => {
  const __import = import("../pages/AdminStaff/Auth/CustomAdminStaffLoginPage");
  __import.finally(() => {});
  return __import;
});
export const CustomAdminStaffSignUpPage = lazy(() => {
  const __import = import(
    "../pages/AdminStaff/Auth/CustomAdminStaffSignUpPage"
  );
  __import.finally(() => {});
  return __import;
});

export const AdminStaffDashboardPage = lazy(() => {
  const __import = import("../pages/AdminStaff/View/AdminStaffDashboardPage");
  __import.finally(() => {});
  return __import;
});

export const ClubListSport = lazy(() => {
  const __import = import("../pages/Club/List/ClubListSport");
  __import.finally(() => {});
  return __import;
});

export const AdminListAdminStaff = lazy(() => {
  const __import = import("../pages/Admin/List/AdminListAdminStaff");
  __import.finally(() => {});
  return __import;
});

export const LandingPage = lazy(() => {
  const __import = import("../pages/Public/LandingPage");
  __import.finally(() => {});
  return __import;
});

export const PublicFAQPage = lazy(() => {
  const __import = import("../pages/Public/PublicFAQPage");
  __import.finally(() => {});
  return __import;
});

export const CoachSetUpStripeMobile = lazy(() => {
  const __import = import("../pages/Coach/View/CoachSetUpStripeMobile");
  __import.finally(() => {});
  return __import;
});

export const StripeOnboardingComplete = lazy(() => {
  const __import = import("../pages/Public/StripeOnboardingComplete");
  __import.finally(() => {});
  return __import;
});

export const AdminStaffVerifyEmail = lazy(() => {
  const __import = import("../pages/AdminStaff/Auth/AdminStaffVerifyEmail");
  __import.finally(() => {});
  return __import;
});

export const AdminStaffOnboarding = lazy(() => {
  const __import = import("../pages/AdminStaff/Auth/AdminStaffOnboarding");
  __import.finally(() => {});
  return __import;
});

export const UserVerifyEmail = lazy(() => {
  const __import = import("../pages/User/Auth/UserVerifyEmail");
  __import.finally(() => {});
  return __import;
});
