import React, { memo, useContext } from "react";
import { Navigate, useLocation } from "react-router";
import { AuthContext } from "Context/Auth";
import metadataJSON from "Utils/metadata.json";
import MkdSDK from "Utils/MkdSDK";
import { updateBrowserTab, updateFavicon } from "Utils/utils";
import { useClub } from "Context/Club";

let sdk = new MkdSDK();
const UserRoute = ({ path, children }) => {
  const Auth = useContext(AuthContext);
  const { club } = useClub();
  const location = useLocation();

  const { isAuthenticated, role } = Auth?.state;

  const user_id = localStorage.getItem("user");

  // console.log("document.title 1", document.title);
  // console.log("clubProfile", clubProfile);
  React.useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];

    if (metadata !== undefined) {
      document.title = `${
        metadata?.description ? metadata?.description : ""
      } | ${club?.name || "Courtmatchup"} `;
    } else {
      updateBrowserTab({
        clubName: club?.name,
        favicon: club?.favicon,
        description: metadata?.description,
      });
    }
  }, [path, club?.name]);

  return (
    <>
      {isAuthenticated ? (
        <>{children}</>
      ) : (
        <Navigate
          to={`/user/login?redirect_uri=${location.pathname}`}
          replace
        />
      )}
    </>
  );
};

export default memo(UserRoute);
