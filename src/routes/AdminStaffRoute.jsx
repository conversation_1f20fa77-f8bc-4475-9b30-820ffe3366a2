import React, { memo, useContext } from "react";
import { Navigate, useLocation } from "react-router";
import { AuthContext } from "Context/Auth";
import metadataJSON from "Utils/metadata.json";

const AdminStaffRoute = ({ path, children }) => {
  const Auth = useContext(AuthContext);
  const location = useLocation();

  const { isAuthenticated, role } = Auth?.state;
  React.useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];
    if (metadata !== undefined) {
      document.title = metadata?.title ? metadata?.title : "courtmatchup";
    } else {
      document.title = "courtmatchup";
    }
  }, [path]);

  return (
    <>
      {isAuthenticated ? (
        <>{children}</>
      ) : (
        <Navigate
          to={`/admin-staff/login?redirect_uri=${location.pathname}`}
          replace
        />
      )}
    </>
  );
};

export default memo(AdminStaffRoute);
