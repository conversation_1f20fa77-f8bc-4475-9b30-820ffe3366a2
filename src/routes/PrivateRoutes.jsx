import React, { memo, useContext } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { NotFound } from "./Routes";
import PublicRoute from "./PublicRoutes";
import ClubRoute from "./ClubRoutes";
import StaffRoute from "./StaffRoutes";
import UserRoute from "./UserRoutes";
import CoachRoute from "./CoachRoutes";
import AdminRoute from "./AdminRoutes";
import AdminStaffRoute from "./AdminStaffRoute";

const PrivateRoute = ({ path, element, access }) => {
  const Auth = useContext(AuthContext);
  const location = useLocation();

  // Add a loading check
  if (Auth?.state?.loading) {
    // You can return a loading spinner or null while auth state is being determined
    return null;
  }

  if (Auth?.state?.isAuthenticated) {
    switch (true) {
      case Auth?.state?.role === "club" && access === "club":
        return <ClubRoute path={path}>{element}</ClubRoute>;
      case Auth?.state?.role === "staff" && access === "staff":
        return <StaffRoute path={path}>{element}</StaffRoute>;
      case Auth?.state?.role === "user" && access === "user":
        return <UserRoute path={path}>{element}</UserRoute>;
      case Auth?.state?.role === "coach" && access === "coach":
        return <CoachRoute path={path}>{element}</CoachRoute>;
      case Auth?.state?.role === "admin" && access === "admin":
        return <AdminRoute path={path}>{element}</AdminRoute>;
      case Auth?.state?.role === "admin_staff" && access === "admin_staff":
        return <AdminStaffRoute path={path}>{element}</AdminStaffRoute>;
      default:
        return <Navigate to="/" replace />;
    }
  }

  // Only redirect to login if we're sure authentication has been checked
  switch (access) {
    case "club":
      return (
        <Navigate
          to={`/club/login?redirect_uri=${location.pathname}`}
          replace
        />
      );
    case "staff":
      return (
        <Navigate
          to={`/staff/login?redirect_uri=${location.pathname}`}
          replace
        />
      );
    case "user":
      return (
        <Navigate
          to={`/user/login?redirect_uri=${location.pathname}`}
          replace
        />
      );
    case "coach":
      return (
        <Navigate
          to={`/coach/login?redirect_uri=${location.pathname}`}
          replace
        />
      );
    case "admin":
      return (
        <Navigate
          to={`/admin/login?redirect_uri=${location.pathname}`}
          replace
        />
      );
    case "admin_staff":
      return (
        <Navigate
          to={`/admin-staff/login?redirect_uri=${location.pathname}`}
          replace
        />
      );
    default:
      return <Navigate to="/" replace />;
  }
};

export default memo(PrivateRoute);
