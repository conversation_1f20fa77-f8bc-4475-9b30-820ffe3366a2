import React, { memo, useContext } from "react";
import { Navigate, useLocation } from "react-router";
import { AuthContext } from "Context/Auth";
import metadataJSON from "Utils/metadata.json";
import { updateBrowserTab, updateFavicon } from "Utils/utils";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();

const CoachRoute = ({ path, children }) => {
  const Auth = useContext(AuthContext);
  const [clubProfile, setClubProfile] = React.useState(null);
  const location = useLocation();
  const { isAuthenticated, role } = Auth?.state;
  const user_id = localStorage.getItem("user");
  async function fetchClubProfile() {
    try {
      sdk.setTable("user");
      const userProfile = await sdk.callRestAPI({ id: user_id }, "GET");

      sdk.setTable("clubs");
      const clubProfile = await sdk.callRestAPI(
        { id: userProfile.model.club_id },
        "GET"
      );
      updateFavicon(clubProfile?.club?.club_logo);
      setClubProfile(clubProfile.model);
      //  userProfile;
    } catch (error) {
      console.log(error);
    }
  }

  React.useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];

    if (metadata !== undefined) {
      document.title = `${
        metadata?.description ? metadata?.description : ""
      } | ${clubProfile?.name || "Courtmatchup"} `;
    } else {
      document.title = ` ${
        metadata?.description ? metadata?.description : ""
      } | ${
        clubProfile?.club?.name ? clubProfile?.club?.name : "Courtmatchup"
      }`;
    }
  }, [path, clubProfile?.club?.name]);
  React.useEffect(() => {
    fetchClubProfile();
  }, []);

  React.useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];
    if (metadata !== undefined) {
      document.title = metadata?.title ? metadata?.title : "courtmatchup";
    } else {
      document.title = "courtmatchup";
    }
  }, [path]);

  return (
    <>
      {isAuthenticated ? (
        <>{children}</>
      ) : (
        <Navigate
          to={`/coach/login?redirect_uri=${location.pathname}`}
          replace
        />
      )}
    </>
  );
};

export default memo(CoachRoute);
