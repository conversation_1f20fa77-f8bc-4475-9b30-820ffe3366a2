import React, { memo, useContext } from "react";
import { Navigate, useLocation } from "react-router";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import metadataJSON from "Utils/metadata.json";
import { updateBrowserTab, updateFavicon } from "Utils/utils";
import { useClub } from "Context/Club";

const StaffRoute = ({ path, children }) => {
  const Auth = useContext(AuthContext);
  const { club } = useClub();
  const location = useLocation();

  const { isAuthenticated, role } = Auth?.state;
  const {
    state: { clubProfile },
  } = React.useContext(GlobalContext);
  React.useEffect(() => {
    const metadata = metadataJSON[path ?? "/"];
    if (metadata !== undefined) {
      document.title = `${
        metadata?.description ? metadata?.description : ""
      } | ${club?.name || "Courtmatchup"} `;
    } else {
      updateBrowserTab({
        clubName: club?.name,
        favicon: club?.club_logo,
        description: metadata?.description,
      });

      updateFavicon(club?.club_logo);
    }
  }, [path, club?.name]);

  return (
    <>
      {isAuthenticated ? (
        <>{children}</>
      ) : (
        <Navigate
          to={`/staff/login?club_id=${club?.id}&redirect_uri=${location.pathname}`}
          replace
        />
      )}
    </>
  );
};

export default memo(StaffRoute);
