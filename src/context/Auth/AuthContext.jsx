import React, { useReducer, useContext } from "react";
import { useLocation, useNavigate } from "react-router";
import MkdSDK from "Utils/MkdSDK";
import { updatedRole } from "Utils/utils";

const initialState = {
  isAuthenticated: false,
  user: null,
  userDetails: {
    firstName: null,
    lastName: null,
    photo: null,
  },
  token: null,
  role: null,
  sessionExpired: null,
  loading: true,
  adminStaffProfile: null,
};

export const AuthContext = React.createContext(initialState);

const reducer = (state, action) => {
  switch (action.type) {
    case "LOGIN":
      localStorage.setItem("user", Number(action.payload.user_id));
      localStorage.setItem("token", action.payload.token);
      localStorage.setItem("role", action.payload.role);
      return {
        ...state,
        isAuthenticated: true,
        user: Number(localStorage.getItem("user")),
        token: localStorage.getItem("token"),
        role: localStorage.getItem("role"),
        userDetails: {
          firstName: action.payload.first_name,
          lastName: action.payload.last_name,
          photo: action.payload.photo,
        },
        loading: false,
      };
    case "UPDATE_PROFILE":
      return {
        ...state,
        role: localStorage.getItem("role"),
        profile: { ...action?.payload },
        loading: false,
      };
    case "UPDATE_ADMIN_STAFF_PROFILE":
      return {
        ...state,
        adminStaffProfile: { ...action?.payload },
        loading: false,
      };
    case "LOGOUT":
      localStorage.removeItem("user");
      localStorage.removeItem("token");
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        adminStaffProfile: null,
        loading: false,
      };
    case "SESSION_EXPIRED":
      return {
        ...state,
        sessionExpired: action?.payload,
        loading: false,
      };
    case "SET_LOADING":
      return {
        ...state,
        loading: action.payload,
      };
    default:
      return state;
  }
};

let sdk = new MkdSDK();

export const tokenExpireError = (dispatch, errorMessage) => {
  /**
   * either this or we pass the role as a parameter
   */
  const role = localStorage.getItem("role");
  if (errorMessage === "TOKEN_EXPIRED") {
    dispatch({ type: "SESSION_EXPIRED", payload: true });
    // dispatch({
    //   type: "LOGOUT",
    // });

    // location.href = "/" + role + "/login";
  }
};

const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const location = useLocation();
  const navigate = useNavigate();

  // Function to fetch admin-staff profile
  const fetchAdminStaffProfile = async () => {
    try {
      const result = await sdk.getAdminStaffProfile();

      if (!result.error) {
        dispatch({
          type: "UPDATE_ADMIN_STAFF_PROFILE",
          payload: result,
        });
      }
    } catch (error) {
      console.error("Error fetching admin-staff profile:", error);
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    const user = localStorage.getItem("user");
    const token = localStorage.getItem("token");
    const role = localStorage.getItem("role");

    if (token) {
      (async function () {
        try {
          await sdk.check(role);

          dispatch({
            type: "LOGIN",
            payload: {
              user_id: user,
              token,
              role: role,
            },
          });

          // If the user is an admin_staff, fetch their profile
          if (role === "admin_staff") {
            await fetchAdminStaffProfile();
          }
        } catch (error) {
          dispatch({
            type: "LOGOUT",
          });
          navigate(
            `/${updatedRole(
              role == "admin_staff" ? "admin-staff" : role,
              location
            )}/login`
          );
        }
      })();
    } else {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  }, []);

  return (
    <AuthContext.Provider
      value={{
        state,
        dispatch,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;

export const useAuth = () => {
  const { state, dispatch } = useContext(AuthContext);
  return { state, dispatch };
};
