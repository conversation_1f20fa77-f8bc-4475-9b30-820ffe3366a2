import { useEffect, useState, useContext } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth/AuthContext";
import { FiMail, FiCheckCircle, FiAlertCircle } from "react-icons/fi";
import { ImSpinner8 } from "react-icons/im";

export default function UserVerifyEmail() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);
  const { dispatch } = useContext(AuthContext);
  useEffect(() => {
    const verifyEmail = async () => {
      const token = searchParams.get("token");

      if (!token) {
        setError("Verification token is missing");
        setLoading(false);
        return;
      }

      try {
        const sdk = new MkdSDK();
        const result = await sdk.verifyEmail(token);

        // Redirect to dashboard after successful verification
        dispatch({
          type: "LOGIN",
          payload: result,
        });
        navigate("/user/dashboard");
      } catch (error) {
        setError(error.message || "Email verification failed");
        setLoading(false);
      }
    };

    verifyEmail();
  }, [searchParams, navigate]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
          <div className="flex flex-col items-center space-y-4">
            <div className="rounded-full bg-blue-50 p-3">
              <ImSpinner8 className="h-8 w-8 animate-spin text-blue-500" />
            </div>
            <h2 className="text-center text-2xl font-semibold text-gray-900">
              Verifying Your Email
            </h2>
            <p className="text-center text-gray-600">
              Please wait while we verify your email address...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
          <div className="flex flex-col items-center space-y-4">
            <div className="rounded-full bg-red-50 p-3">
              <FiAlertCircle className="h-8 w-8 text-red-500" />
            </div>
            <h2 className="text-center text-2xl font-semibold text-gray-900">
              Verification Failed
            </h2>
            <p className="text-center text-red-500">{error}</p>
            <button
              onClick={() => navigate("/user/login")}
              className="mt-4 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Return to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
        <div className="flex flex-col items-center space-y-4">
          <div className="rounded-full bg-green-50 p-3">
            <FiCheckCircle className="h-8 w-8 text-green-500" />
          </div>
          <h2 className="text-center text-2xl font-semibold text-gray-900">
            Email Verified!
          </h2>
          <p className="text-center text-gray-600">
            Your email has been successfully verified. Redirecting to
            dashboard...
          </p>
        </div>
      </div>
    </div>
  );
}
