import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { InteractiveButton } from "Components/InteractiveButton";
import { FiUsers, <PERSON>Check, FiX, FiMail } from "react-icons/fi";

let sdk = new MkdSDK();

export default function UserAcceptInvite() {
  const { id: inviteGroupId } = useParams();
  const navigate = useNavigate();
  const [currentInvite, setCurrentInvite] = useState(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [message, setMessage] = useState(null);
  const [error, setError] = useState(null);
  const [inviteProcessed, setInviteProcessed] = useState(false);

  // Fetch the specific invite details
  const fetchInviteDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      const pendingInvitesResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/groups/pending-invites`,
        {},
        "GET"
      );

      if (pendingInvitesResponse.invites) {
        const invite = pendingInvitesResponse.invites.find(
          (invite) => invite.group_id === parseInt(inviteGroupId)
        );

        if (invite) {
          setCurrentInvite(invite);
        } else {
          setError("Invite not found or may have already been processed.");
        }
      } else {
        setError("No pending invites found.");
      }
    } catch (error) {
      setError("Error fetching invite details: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle accept invite
  const handleAcceptInvite = async () => {
    if (!currentInvite) return;

    setActionLoading(true);
    setMessage(null);
    setError(null);

    try {
      const payload = {
        invite_id: currentInvite.invite_id,
        status: "accepted",
      };

      await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/groups/invite-response`,
        payload,
        "POST"
      );

      setMessage(
        "Invite accepted successfully! You are now part of the group."
      );
      setInviteProcessed(true);

      // Redirect to groups page after a delay
      setTimeout(() => {
        navigate("/user/my-groups");
      }, 2000);
    } catch (error) {
      setError("Error accepting invite: " + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  // Handle reject invite
  const handleRejectInvite = async () => {
    if (!currentInvite) return;

    setActionLoading(true);
    setMessage(null);
    setError(null);

    try {
      const payload = {
        invite_id: currentInvite.invite_id,
        status: "rejected",
      };

      await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/groups/invite-response`,
        payload,
        "POST"
      );

      setMessage("Invite rejected successfully.");
      setInviteProcessed(true);

      // Redirect to groups page after a delay
      setTimeout(() => {
        navigate("/user/my-groups");
      }, 2000);
    } catch (error) {
      setError("Error rejecting invite: " + error.message);
    } finally {
      setActionLoading(false);
    }
  };

  useEffect(() => {
    fetchInviteDetails();
  }, [inviteGroupId]);

  // Loading state
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primaryBlue border-t-transparent"></div>
          <p className="text-gray-600">Loading invite details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md">
        {/* Main Invite Card */}
        <div className="rounded-2xl bg-white p-6 shadow-lg">
          {/* Header */}
          <div className="mb-6 text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primaryBlue/10">
              <FiUsers className="h-8 w-8 text-primaryBlue" />
            </div>
            <h1 className="text-2xl font-semibold text-gray-900">
              Group Invitation
            </h1>
            <p className="mt-2 text-gray-600">
              You've been invited to join a group
            </p>
          </div>

          {/* Invite Details */}
          {currentInvite && !inviteProcessed && (
            <div className="mb-6 space-y-4">
              <div className="rounded-lg bg-gray-50 p-4">
                <div className="flex items-center space-x-3">
                  <FiUsers className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {currentInvite.group_name || "Group"}
                    </p>
                    <p className="text-sm text-gray-600">
                      Group ID: {currentInvite.group_id}
                    </p>
                  </div>
                </div>
              </div>

              {currentInvite.inviter_name && (
                <div className="rounded-lg bg-gray-50 p-4">
                  <div className="flex items-center space-x-3">
                    <FiMail className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-600">Invited by</p>
                      <p className="font-medium text-gray-900">
                        {currentInvite.inviter_name}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          {currentInvite && !inviteProcessed && (
            <div className="space-y-3">
              <InteractiveButton
                onClick={handleAcceptInvite}
                loading={actionLoading}
                className="w-full rounded-xl bg-primaryGreen px-6 py-3 text-white hover:bg-primaryGreen/90"
              >
                <FiCheck className="mr-2 h-5 w-5" />
                Accept Invitation
              </InteractiveButton>

              <InteractiveButton
                onClick={handleRejectInvite}
                loading={actionLoading}
                className="w-full rounded-xl border border-gray-300 bg-white px-6 py-3 text-gray-700 hover:bg-gray-50"
              >
                <FiX className="mr-2 h-5 w-5" />
                Decline Invitation
              </InteractiveButton>
            </div>
          )}

          {/* Status Messages */}
          {message && (
            <div className="mt-4 rounded-lg bg-green-50 p-4">
              <div className="flex items-center">
                <FiCheck className="mr-2 h-5 w-5 text-green-500" />
                <p className="text-green-700">{message}</p>
              </div>
            </div>
          )}

          {error && (
            <div className="mt-4 rounded-lg bg-red-50 p-4">
              <div className="flex items-center">
                <FiX className="mr-2 h-5 w-5 text-red-500" />
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Back to Groups Link */}
          {(inviteProcessed || error) && (
            <div className="mt-6 text-center">
              <button
                onClick={() => navigate("/user/my-groups")}
                className="font-medium text-primaryBlue hover:text-primaryBlue/80"
              >
                Go to My Groups
              </button>
            </div>
          )}
        </div>

        {/* Additional Info */}
        {!inviteProcessed && !error && currentInvite && (
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-500">
              This invitation will allow you to join the group and participate
              in group activities.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
