import React, { useEffect, useState, useRef } from "react";
import { IoSearchOutline } from "react-icons/io5";
import { IoIosArrowDown } from "react-icons/io";
import { LuAlignLeft } from "react-icons/lu";
import { IoPrintOutline } from "react-icons/io5";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { tokenExpireError } from "Context/Auth";
import { fCurrency } from "Utils/formatNumber";
import LoadingSpinner from "Components/LoadingSpinner";
import { eventTypeOptions } from "Utils/utils";

let sdk = new MkdSDK();
export default function UserBilling() {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [expandedItems, setExpandedItems] = useState({});
  const [currentTableData, setCurrentTableData] = useState([]);
  const [dataTotal, setDataTotal] = useState(0);
  const [canPreviousPage, setCanPreviousPage] = useState(false);
  const [canNextPage, setCanNextPage] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [sortOrder, setSortOrder] = useState("desc"); // "asc" or "desc"
  const [loading, setLoading] = useState(false);
  const [printingInvoiceId, setPrintingInvoiceId] = useState(null);
  const receiptRefs = useRef({});

  const toggleItem = (item) => {
    setExpandedItems((prev) => ({
      ...prev,
      [item]: !prev[item],
    }));
  };

  const fetchInvoices = async (filters = {}) => {
    setLoading(true);
    try {
      let queryParams = new URLSearchParams();

      if (filters.sort) {
        queryParams.append("sort", filters.sort);
      }

      if (filters.invoice_type) {
        queryParams.append("invoice_type", filters.invoice_type);
      }
      if (filters.search) {
        queryParams.append("search", filters.search);
      }

      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/billing/invoices${
          queryParams.toString() ? `?${queryParams.toString()}` : ""
        }`,
        {},
        "GET"
      );

      if (response.error) {
        showToast(globalDispatch, response.message, 5000);
        return;
      }

      setCurrentTableData(response.invoices || []);
    } catch (error) {
      console.error("ERROR", error);
      showToast(globalDispatch, error.message, 5000);
      tokenExpireError(dispatch, error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    fetchInvoices({
      sort: sortOrder,
    });
  };

  const getFilteredData = () => {
    if (!startDate && !endDate) return currentTableData;

    return currentTableData.filter((invoice) => {
      try {
        const invoiceDate = new Date(invoice.date);
        // Check if date is valid before converting to ISO string
        if (isNaN(invoiceDate.getTime())) {
          return false;
        }

        const start = startDate ? new Date(startDate) : null;
        const end = endDate ? new Date(endDate) : null;

        if (start && end) {
          return invoiceDate >= start && invoiceDate <= end;
        } else if (start) {
          return invoiceDate >= start;
        } else if (end) {
          return invoiceDate <= end;
        }

        return true;
      } catch (error) {
        console.error("Invalid date:", invoice.date);
        return false;
      }
    });
  };

  const toggleSortOrder = () => {
    const newOrder = sortOrder === "asc" ? "desc" : "asc";
    setSortOrder(newOrder);
    fetchInvoices({
      sort: newOrder,
    });
  };

  useEffect(() => {
    fetchInvoices({
      sort: sortOrder,
    });
  }, []);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatAmount = (amount) => {
    return Number(amount).toLocaleString("en-US", {
      style: "currency",
      currency: "usd",
    });
  };

  const handlePrintReceipt = (invoiceId) => {
    // Set the current invoice to print
    setPrintingInvoiceId(invoiceId);

    // Create a new window for printing
    const printWindow = window.open("", "_blank");

    if (!printWindow) {
      showToast(globalDispatch, "Please allow pop-ups to print receipts", 5000);
      return;
    }

    // Get the receipt content
    const receiptContent = receiptRefs.current[invoiceId];
    if (!receiptContent) {
      showToast(globalDispatch, "Receipt content not found", 5000);
      return;
    }

    // Get the invoice data
    const invoice = currentTableData.find((inv) => inv.id === invoiceId);

    // Create the print document
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt #${invoice.receipt_id}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .receipt-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 1px solid #eee;
              padding-bottom: 20px;
            }
            .receipt-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .receipt-id {
              font-size: 16px;
              color: #666;
            }
            .receipt-date {
              font-size: 14px;
              color: #666;
              margin-top: 5px;
            }
            .receipt-body {
              margin-bottom: 30px;
            }
            .receipt-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
              padding: 5px 0;
              border-bottom: 1px solid #f5f5f5;
            }
            .receipt-label {
              font-weight: 500;
              color: #666;
            }
            .receipt-total {
              margin-top: 20px;
              font-size: 18px;
              font-weight: bold;
              border-top: 2px solid #eee;
              padding-top: 10px;
            }
            .receipt-footer {
              margin-top: 40px;
              text-align: center;
              font-size: 14px;
              color: #999;
            }
            @media print {
              body {
                padding: 0;
                margin: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-header">
            <div class="receipt-title">Payment Receipt</div>
            <div class="receipt-id">Invoice #${invoice.receipt_id}</div>
            <div class="receipt-date">Date: ${formatDate(invoice.date)}</div>
          </div>

          <div class="receipt-body">
            <div class="receipt-row">
              <span class="receipt-label">Type:</span>
              <span>${invoice.type}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Amount:</span>
              <span>${formatAmount(invoice.amount)}</span>
            </div>
            ${
              invoice.coach_fee > 0
                ? `
            <div class="receipt-row">
              <span class="receipt-label">Coach Fee:</span>
              <span>${formatAmount(invoice.coach_fee)}</span>
            </div>
            `
                : ""
            }
            ${
              invoice.service_fee > 0
                ? `
            <div class="receipt-row">
              <span class="receipt-label">Service Fee:</span>
              <span>${formatAmount(invoice.service_fee)}</span>
            </div>
            `
                : ""
            }
            ${
              invoice.club_fee > 0
                ? `
            <div class="receipt-row">
              <span class="receipt-label">Club Fee:</span>
              <span>${formatAmount(invoice.club_fee)}</span>
            </div>
            `
                : ""
            }
            <div class="receipt-row">
              <span class="receipt-label">Valid Until:</span>
              <span>${formatDate(invoice.valid_until)}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Payment Method:</span>
              <span>${invoice.payment_method}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Status:</span>
              <span class="capitalize">${invoice.status}</span>
            </div>

            <div class="receipt-total">
              <div class="receipt-row">
                <span class="receipt-label">Total:</span>
                <span>${formatAmount(invoice.total_amount)}</span>
              </div>
            </div>
          </div>

          <div class="receipt-footer">
            Thank you for your payment.
          </div>

          <script>
            window.onload = function() {
              window.print();
              setTimeout(function() {
                window.close();
              }, 500);
            };
          </script>
        </body>
      </html>
    `);

    printWindow.document.close();

    // Reset the printing invoice ID after a delay
    setTimeout(() => {
      setPrintingInvoiceId(null);
    }, 1000);
  };

  return (
    <div className="mx-auto max-w-3xl p-4 sm:p-6">
      {loading && <LoadingSpinner />}
      {/* Header */}
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold">Billing</h1>
        <select
          className="w-full rounded-lg border-gray-200 px-4 py-2 capitalize sm:w-auto"
          onChange={(e) =>
            fetchInvoices({ invoice_type: e.target.value, sort: sortOrder })
          }
        >
          <option value="">All bills</option>
          <option value="subscription">Subscription</option>
          <option value="lesson">Lesson</option>
          <option value="clinic">Clinic</option>
          <option value="club_court">Club Court</option>
        </select>
      </div>
      <div className="mb-5 border-b border-gray-200"></div>

      {/* Search and Filters */}
      <div className="mb-6 flex flex-col gap-3 sm:flex-row">
        <div className="relative flex-1">
          <IoSearchOutline className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearch}
            placeholder="Search by plan name or invoice number"
            className="w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="flex w-full gap-2 sm:w-auto">
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            placeholder="Start date"
            className="w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"
          />
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            placeholder="End date"
            className="w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"
          />
          {(startDate || endDate) && (
            <button
              onClick={() => {
                setStartDate("");
                setEndDate("");
              }}
              className="rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700"
              title="Clear dates"
            >
              ✕
            </button>
          )}
        </div>
        <button
          onClick={toggleSortOrder}
          className="flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto"
        >
          {sortOrder === "asc" ? "Oldest first" : "Latest first"}
          <LuAlignLeft
            className={`transform ${sortOrder === "desc" ? "rotate-180" : ""}`}
          />
        </button>
      </div>

      {/* Billing Items */}
      <div className="space-y-4">
        {getFilteredData().length === 0 ? (
          <div className="py-8 text-center text-gray-500">
            No billing records found
          </div>
        ) : (
          getFilteredData().map((invoice) => (
            <div
              key={invoice.id}
              className="rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm"
            >
              <button
                onClick={() => toggleItem(invoice.id)}
                className="flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between"
              >
                <div className="flex items-center gap-2">
                  <IoIosArrowDown
                    className={`transform transition-transform ${
                      expandedItems[invoice.id] ? "" : "-rotate-90"
                    }`}
                  />
                  <span className="text-sm sm:text-base">{invoice.type}</span>
                </div>
                <div className="flex items-center justify-between gap-4 pl-6 sm:pl-0">
                  <span className="text-sm text-gray-600">
                    {formatDate(invoice.create_at)}
                  </span>
                  <span className="font-medium">
                    {formatAmount(invoice.total_amount)}
                  </span>
                </div>
              </button>
              {expandedItems[invoice.id] && (
                <div className="mt-2 rounded-lg bg-white p-4">
                  <div
                    className="space-y-4"
                    ref={(el) => (receiptRefs.current[invoice.id] = el)}
                  >
                    <div className="flex flex-col justify-between gap-1 sm:flex-row">
                      <span className="text-gray-600">Amount</span>
                      <span>{formatAmount(invoice.amount)}</span>
                    </div>

                    {invoice.coach_fee > 0 && (
                      <div className="flex flex-col justify-between gap-1 sm:flex-row">
                        <span className="text-gray-600">Coach fee</span>
                        <span>{formatAmount(invoice.coach_fee)}</span>
                      </div>
                    )}
                    {invoice.service_fee > 0 && (
                      <div className="flex flex-col justify-between gap-1 sm:flex-row">
                        <span className="text-gray-600">Service fee</span>
                        <span>{formatAmount(invoice.service_fee)}</span>
                      </div>
                    )}
                    {invoice.club_fee > 0 && (
                      <div className="flex flex-col justify-between gap-1 sm:flex-row">
                        <span className="text-gray-600">Club fee</span>
                        <span>{formatAmount(invoice.club_fee)}</span>
                      </div>
                    )}
                    <div className="flex flex-col justify-between gap-1 sm:flex-row">
                      <span className="text-gray-600">Invoice ID</span>
                      <span>#{invoice.receipt_id}</span>
                    </div>
                    <div className="flex flex-col justify-between gap-1 sm:flex-row">
                      <span className="text-gray-600">Date</span>
                      <span>{formatDate(invoice.date)}</span>
                    </div>
                    <div className="flex flex-col justify-between gap-1 sm:flex-row">
                      <span className="text-gray-600">Valid until</span>
                      <span>{formatDate(invoice.valid_until)}</span>
                    </div>
                    <div className="flex flex-col justify-between gap-1 sm:flex-row">
                      <span className="text-gray-600">Payment method</span>
                      <span>{invoice.payment_method}</span>
                    </div>
                    <div className="flex flex-col justify-between gap-1 sm:flex-row">
                      <span className="text-gray-600">Status</span>
                      <span className="capitalize">{invoice.status}</span>
                    </div>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePrintReceipt(invoice.id);
                      }}
                      className="mt-4 flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                    >
                      <IoPrintOutline className="text-lg" />
                      Print Receipt
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}
