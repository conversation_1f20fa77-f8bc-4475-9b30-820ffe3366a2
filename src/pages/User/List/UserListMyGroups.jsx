import React, { useState, useEffect } from "react";
import {
  FiUsers,
  FiX,
  FiChevronRight,
  FiMail,
  FiUserPlus,
} from "react-icons/fi";
import GroupCard from "../../../components/GroupCard";
import RightSideModal from "../../../components/RightSideModal";
import AddFamilyMemberForm from "../../../components/AddFamilyMemberForm";
import CreateGroupForm from "../../../components/CreateGroupForm";
import UserProfileModal from "../../../components/UserProfileModal";
import TreeSDK from "Utils/TreeSDK";
import MkdSDK from "Utils/MkdSDK";
import { getManyByIds, GlobalContext, showToast } from "Context/Global";
import { AuthContext } from "Context/Auth";
import LoadingSpinner from "Components/LoadingSpinner";
import InviteToCourtmatch from "Components/InviteToCourtmatch";
import DeleteModal from "Components/Modals/DeleteModal";
import AddExistingUserForm from "Components/AddExistingUserForm";
import EditGroupNameModal from "Components/EditGroupNameModal";
import { actionLogTypes, activityLogTypes } from "Utils/utils";
import { useClub } from "Context/Club";
let tdk = new TreeSDK();
let sdk = new MkdSDK();

export default function UserListMyGroups() {
  const [groups, setGroups] = useState([]);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  const [showAddFamilyMemberModal, setShowAddFamilyMemberModal] =
    useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [users, setUsers] = useState([]);
  const [submitForm, setSubmitForm] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [inviteModal, setInviteModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [selectedMember, setSelectedMember] = useState(null);
  const [showRemoveMemberModal, setShowRemoveMemberModal] = useState(false);
  const [removeMemberLoading, setRemoveMemberLoading] = useState(false);
  const [editGroupNameModal, setEditGroupNameModal] = useState(false);
  const { club, user_profile, triggerRefetch } = useClub();
  const [showAddExistingUserModal, setShowAddExistingUserModal] =
    useState(false);
  const [pendingInvites, setPendingInvites] = useState([]);
  const [sentInvites, setSentInvites] = useState([]);

  const user_id = localStorage.getItem("user");

  const fetchClubUsers = async () => {
    const user_id = localStorage.getItem("user");
    try {
      const usersResponse = await tdk.getList("user", {
        filter: [`role,cs,user`, `club_id,eq,${club?.id}`],
      });
      const usersList = usersResponse.list.filter(
        (user) => user?.id != user_id
      );
      setUsers(usersList);
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const fetchPendingInvites = async () => {
    try {
      const pendingInvitesResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/groups/pending-invites`,
        {},
        "GET"
      );
      setPendingInvites(pendingInvitesResponse.pending_invites);
    } catch (error) {
      console.error("Error fetching pending invites:", error);
    }
  };
  const fetchSentInvites = async () => {
    try {
      const sentInvitesResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/groups/sent-invites`,
        {},
        "GET"
      );
      console.log("sent invites", sentInvitesResponse);
      if (!sentInvitesResponse.error && sentInvitesResponse.invites) {
        setSentInvites(sentInvitesResponse.invites);
      }
    } catch (error) {
      console.error("Error fetching sent invites:", error);
    }
  };

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const userGroupsResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/groups?type=0`,
        {},
        "GET"
      );

      setGroups(userGroupsResponse.groups);
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    fetchPendingInvites();
    fetchSentInvites();
  }, [triggerRefetch]);

  useEffect(() => {
    fetchClubUsers();
  }, [triggerRefetch]);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "my-groups",
      },
    });
  }, []);

  const handleAddMember = (groupId) => {
    setSelectedGroupId(groupId);
    setShowAddMemberModal(true);
  };

  const handleViewProfile = (memberId) => {
    const member = groups
      .flatMap((group) => group.members)
      .find((member) => member.id === memberId);

    if (member) {
      setSelectedUser(member);
      setShowProfileModal(true);
    }
  };

  const handleRemoveMember = async () => {
    setRemoveMemberLoading(true);
    try {
      const members = selectedGroup.members?.map((g) => g.id);
      const updatedMembers = members.filter((m) => m !== selectedMember.id);

      const payload = {
        id: selectedGroup.id,
        members: JSON.stringify(updatedMembers),
      };

      sdk.setTable("user_groups");
      const response = await sdk.callRestAPI(payload, "PUT");
      sdk.setTable("activity_logs");
      await sdk.callRestAPI(
        {
          user_id: user_id,
          action: "Removed member from group",
          activity_type: activityLogTypes.group,
          action_type: actionLogTypes.DELETE,
          data: JSON.stringify(payload),
          club_id: club?.id,
          description: "Removed member from group",
        },
        "POST"
      );
      setSelectedGroup(null);
      setSelectedMember(null);
      setShowRemoveMemberModal(false);
      showToast(globalDispatch, "Member removed successfully");
      fetchData();
    } catch (error) {
      console.error("Error removing member:", error);
      showToast(globalDispatch, error.message, 3000, "error");
    } finally {
      setRemoveMemberLoading(false);
    }
  };

  const handleDeleteGroup = async () => {
    setDeleteLoading(true);
    try {
      sdk.setTable("user_groups");
      await sdk.callRestAPI({ id: selectedGroup.group_id }, "DELETE");

      sdk.setTable("activity_logs");
      await sdk.callRestAPI(
        {
          user_id: user_id,
          action: "Deleted group",
          type: activityLogTypes.delete_group,
          data: JSON.stringify({ group: selectedGroup }),
          club_id: club?.id,
        },
        "POST"
      );
      setDeleteModal(false);
      setSelectedGroup(null);
      fetchData();
    } catch (error) {
      console.error("Error deleting group:", error);
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleAddFamilyMember = async (formData) => {
    setSubmitting(true);
    try {
      // Implement the API call to add family member
      console.log("Adding family member:", formData);
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call
      setShowAddFamilyMemberModal(false);
    } catch (error) {
      console.error("Error adding family member:", error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div>
      {isLoading && <LoadingSpinner />}
      <div className="flex items-center justify-between bg-white px-4 py-4">
        <h1 className="mb-6 text-2xl font-semibold">My groups</h1>

        <button
          onClick={() => setShowCreateGroupModal(true)}
          className="rounded-lg bg-primaryBlue px-3 py-2 text-white"
        >
          Create group
        </button>
      </div>

      <div className="mx-auto mt-5 max-w-7xl p-5">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {groups?.length > 0 ? (
            groups?.map((group) => (
              <GroupCard
                key={group.id}
                group={group}
                sentInvites={sentInvites}
                onAddExistingUser={() => {
                  setSelectedGroup(group);
                  setShowAddExistingUserModal(true);
                }}
                onAddMember={handleAddMember}
                onEditName={() => {
                  setEditGroupNameModal(true);
                  setSelectedGroup(group);
                }}
                onDeleteGroup={() => {
                  setDeleteModal(true);
                  setSelectedGroup(group);
                }}
                onViewProfile={handleViewProfile}
                onRemoveMember={(group, member) => {
                  setShowRemoveMemberModal(true);
                  setSelectedMember(member);
                  setSelectedGroup(group);
                }}
                onAddFamilyMember={() => {
                  setShowAddFamilyMemberModal(true);
                  setSelectedGroup(group);
                }}
                onInviteToCourtmatch={() => {
                  setInviteModal(true);
                  setSelectedGroup(group);
                }}
              />
            ))
          ) : (
            <div className="col-span-3">
              <p className="text-center text-gray-500">No groups found</p>
            </div>
          )}
        </div>

        {/* Add Member Modal */}
        {showAddMemberModal && (
          <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-96 rounded-lg bg-white p-6">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-medium">Add Member</h3>
                <button
                  onClick={() => setShowAddMemberModal(false)}
                  className="rounded hover:bg-gray-100"
                >
                  <FiX className="h-5 w-5" />
                </button>
              </div>
              <div className="space-y-4">
                <button
                  className="flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50"
                  onClick={() => {
                    setShowAddMemberModal(false);
                    setShowAddExistingUserModal(true);
                    setSelectedGroup(selectedGroup);
                  }}
                >
                  <div className="flex items-center">
                    <FiUserPlus className="mr-2" />
                    <span>Add existing user</span>
                  </div>
                  <FiChevronRight />
                </button>
                <button
                  className="flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50"
                  onClick={() => {
                    setShowAddMemberModal(false);
                    setShowAddFamilyMemberModal(true);
                    setSelectedGroup(selectedGroup);
                  }}
                >
                  <div className="flex items-center">
                    <FiUsers className="mr-2" />
                    <span>Add family member</span>
                  </div>
                  <FiChevronRight />
                </button>
                <button className="flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50">
                  <div className="flex items-center">
                    <FiMail className="mr-2" />
                    <span>Invite to CourtMatch</span>
                  </div>
                  <FiChevronRight />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Add Family Member Modal */}
        <RightSideModal
          isOpen={showAddFamilyMemberModal}
          title="Add family member"
          showFooter={false}
          onClose={() => {
            setShowAddFamilyMemberModal(false);
            setSelectedGroup(null);
          }}
        >
          <AddFamilyMemberForm
            users={users}
            user={user_profile}
            fetchData={fetchData}
            group={selectedGroup}
            onSubmit={handleAddFamilyMember}
            onClose={() => {
              setShowAddFamilyMemberModal(false);
              setSelectedGroup(null);
            }}
          />
        </RightSideModal>

        {/* Create Group Modal */}
        <RightSideModal
          isOpen={showCreateGroupModal}
          onClose={() => setShowCreateGroupModal(false)}
          title="Create group"
          showFooter={false}
        >
          <CreateGroupForm
            users={users}
            fetchData={fetchData}
            user={user_profile}
            onClose={() => setShowCreateGroupModal(false)}
          />
        </RightSideModal>

        {/* Add Existing User Modal */}
        <RightSideModal
          isOpen={showAddExistingUserModal}
          onClose={() => {
            setShowAddExistingUserModal(false);
            setSelectedGroup(null);
          }}
          showFooter={false}
          title={`Add user to ${selectedGroup?.group_name}`}
        >
          <AddExistingUserForm
            user={user_profile}
            users={users}
            group={selectedGroup}
            fetchData={() => {
              fetchPendingInvites();
              fetchSentInvites();
            }}
            sentInvites={sentInvites}
            onClose={() => {
              setShowAddExistingUserModal(false);
              setSelectedGroup(null);
            }}
          />
        </RightSideModal>

        {/* Edit Group Name Modal */}
        {editGroupNameModal && (
          <EditGroupNameModal
            title={`Edit ${selectedGroup?.group_name}`}
            group={selectedGroup}
            user={user_profile}
            onClose={(shouldRefetch) => {
              setEditGroupNameModal(false);
              setSelectedGroup(null);
              if (shouldRefetch) {
                fetchData();
              }
            }}
          />
        )}

        {/* User Profile Modal */}
        <UserProfileModal
          isOpen={showProfileModal}
          onClose={() => {
            setShowProfileModal(false);
            setSelectedUser(null);
          }}
          fetchData={fetchData}
          user={selectedUser}
        />

        {inviteModal && (
          <InviteToCourtmatch
            user={user_profile}
            title="Invite a friend to Court Matchup"
            onClose={() => {
              setInviteModal(false);
              setSelectedGroup(null);
            }}
            group={selectedGroup}
          />
        )}

        <DeleteModal
          isOpen={deleteModal}
          onClose={() => setDeleteModal(false)}
          title="Delete group"
          requireConfirmation={true}
          message={`Are you sure you want to delete ${selectedGroup?.group_name} with ${selectedGroup?.members?.length} members?`}
          onDelete={handleDeleteGroup}
          buttonText="Delete"
          loading={deleteLoading}
        />

        <DeleteModal
          isOpen={showRemoveMemberModal}
          onClose={() => {
            setShowRemoveMemberModal(false);
            setSelectedGroup(null);
            setSelectedMember(null);
          }}
          title="Remove member"
          message={`Are you sure you want to remove ${selectedMember?.first_name} ${selectedMember?.last_name} from ${selectedGroup?.group_name}?`}
          onDelete={handleRemoveMember}
          requireConfirmation={true}
          buttonText="Remove"
          loading={removeMemberLoading}
        />
      </div>
    </div>
  );
}
