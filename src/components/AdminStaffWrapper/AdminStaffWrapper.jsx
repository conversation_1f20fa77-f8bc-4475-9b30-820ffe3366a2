import React, { Suspense, memo, useEffect } from "react";

import { AdminHeader } from "Components/AdminHeader";
import { TopHeader } from "Components/TopHeader";
import { Spinner } from "Assets/svgs";
import { LazyLoad } from "Components/LazyLoad";
import { AuthContext } from "Context/Auth";
import { useNavigate } from "react-router";
import MkdSDK from "Utils/MkdSDK";

const AdminStaffWrapper = ({ children }) => {
  const { state: authState, dispatch } = React.useContext(AuthContext);
  const sdk = new MkdSDK();
  const navigate = useNavigate();

  // Log the admin-staff profile when it changes
  useEffect(() => {
    if (authState.adminStaffProfile) {
      console.log("Admin Staff Profile:", authState.adminStaffProfile);
    }
  }, [authState.adminStaffProfile]);

  // Fetch admin-staff profile if not already loaded
  useEffect(() => {
    const fetchAdminStaffProfile = async () => {
      try {
        // Only fetch if user is admin_staff and profile isn't already loaded
        if (authState.role === "admin_staff" && !authState.adminStaffProfile) {
          const result = await sdk.getAdminStaffProfile();

          console.log("admin staff profile result", result);

          if (result.completed == 0) {
            navigate("/admin-staff/onboarding");
          }

          if (!result.error) {
            dispatch({
              type: "UPDATE_ADMIN_STAFF_PROFILE",
              payload: result,
            });
          }
        }
      } catch (error) {
        console.error("Error fetching admin-staff profile:", error);
      }
    };

    fetchAdminStaffProfile();
  }, [authState.role, authState.adminStaffProfile]);

  return (
    <LazyLoad>
      <div
        id="admin_wrapper"
        className={`flex w-full max-w-full flex-col bg-white`}
      >
        <div className={`flex min-h-screen w-full max-w-full `}>
          <AdminHeader />
          <div className={`w-full overflow-hidden`}>
            <TopHeader />
            <Suspense
              fallback={
                <div
                  className={`flex h-screen w-full items-center justify-center`}
                >
                  {/* <Spinner size={100} color="#2CC9D5" /> */}
                </div>
              }
            >
              <div className="w-full overflow-y-auto overflow-x-hidden">
                {children}
              </div>
            </Suspense>
          </div>
        </div>
      </div>
    </LazyLoad>
  );
};

export default memo(AdminStaffWrapper);
