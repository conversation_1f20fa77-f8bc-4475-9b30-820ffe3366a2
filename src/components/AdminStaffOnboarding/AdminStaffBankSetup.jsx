import { InteractiveButton } from "Components/InteractiveButton";
import React, { useState, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";

const sdk = new MkdSDK();

function AdminStaffBankSetup({
  onNext,
  onBack,
  stripeConnectionData,
  isSubmitting,
  setStripeConnectionData,
}) {
  const [submitting] = React.useState(isSubmitting || false);
  const [stripeConnectLoading, setStripeConnectLoading] = useState(false);
  const [continueLoading, setContinueLoading] = useState(false);
  const [skipPaymentLoading, setSkipPaymentLoading] = useState(false);

  const userRole = localStorage.getItem("role");

  const checkStripeConnection = async () => {
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/${userRole}/stripe/account/verify`,
        {},
        "POST"
      );
      setStripeConnectionData(response);
      return response;
    } catch (error) {
      console.error("Error checking Stripe connection:", error);
      return false;
    }
  };

  const handleConnectStripe = async () => {
    setStripeConnectLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/${userRole}/stripe/onboarding`,
        {},
        "POST"
      );
      // Open the Stripe onboarding URL in a new tab
      if (response && response.url) {
        window.open(response.url, "_blank");
      }
    } catch (error) {
      console.error("Error connecting to Stripe:", error);
    }
    setStripeConnectLoading(false);
  };

  // Add an effect to check Stripe connection status after connecting
  useEffect(() => {
    if (stripeConnectLoading === false) {
      // Check connection status after the connect button is clicked and loading is done
      const timer = setTimeout(() => {
        checkStripeConnection();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [stripeConnectLoading]);

  const handleContinue = async () => {
    setContinueLoading(true);
    await onNext();
    setContinueLoading(false);
  };

  const handleSkipPayment = async () => {
    setSkipPaymentLoading(true);
    try {
      // Set account_details to indicate admin staff won't be paid through platform
      await onNext({ skip_payment: true });
    } finally {
      setSkipPaymentLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-2xl px-6">
      <div className="mb-8 text-center">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          Connect Your Bank Account
        </h1>
        <p className="text-gray-600">
          Set up your payment method to receive payments through the platform
        </p>
      </div>

      <div className="space-y-6">
        {/* Stripe Connection Status */}
        <div className="rounded-lg border border-gray-200 bg-white p-6">
          {stripeConnectionData?.complete ||
          stripeConnectionData?.details_submitted ? (
            <div className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">
                Stripe Account Connected
              </h3>
              <p className="mt-2 text-sm text-gray-600">
                Your Stripe account has been successfully connected and verified.
              </p>
              
              {/* Connection Details */}
              <div className="mt-4 grid grid-cols-2 gap-4">
                <div className="flex flex-col items-center rounded-md border bg-white p-2">
                  <div
                    className={`h-3 w-3 rounded-full ${
                      stripeConnectionData.details_submitted
                        ? "bg-green-500"
                        : "bg-gray-300"
                    }`}
                  ></div>
                  <span className="mt-1 text-xs text-gray-600">
                    Details Submitted
                  </span>
                </div>
                <div className="flex flex-col items-center rounded-md border bg-white p-2">
                  <div
                    className={`h-3 w-3 rounded-full ${
                      stripeConnectionData.charges_enabled
                        ? "bg-green-500"
                        : "bg-gray-300"
                    }`}
                  ></div>
                  <span className="mt-1 text-xs text-gray-600">
                    Charges Enabled
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <svg
                  className="h-6 w-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">
                Connect Stripe Account
              </h3>
              <p className="mt-2 text-sm text-gray-600">
                Connect your Stripe account to receive payments from the platform.
                This is secure and handled by Stripe.
              </p>
              
              <button
                onClick={handleConnectStripe}
                disabled={stripeConnectLoading}
                className="mt-4 w-full rounded-xl bg-primaryBlue px-4 py-3 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
              >
                {stripeConnectLoading ? "Connecting..." : "Connect Stripe Account"}
              </button>
            </div>
          )}
        </div>

        {/* Information Box */}
        <div className="rounded-lg bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-blue-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                About Stripe Integration
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <ul className="list-disc space-y-1 pl-5">
                  <li>Stripe handles all payment processing securely</li>
                  <li>Your banking information is never stored on our servers</li>
                  <li>You can manage your account directly through Stripe</li>
                  <li>Payments are processed according to your Stripe settings</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="mt-8">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Step 3 of 3</span>
            <span>Bank Setup</span>
          </div>
          <div className="mt-2 h-2 w-full rounded-full bg-gray-200">
            <div className="h-2 w-full rounded-full bg-primaryGreen"></div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4 pt-6">
          {stripeConnectionData?.complete ||
          stripeConnectionData?.details_submitted ? (
            <div className="flex gap-4">
              <InteractiveButton
                type="button"
                onClick={onBack}
                className="flex-1 rounded-xl border border-gray-300 bg-white px-4 py-3 text-gray-700 hover:bg-gray-50"
              >
                Back
              </InteractiveButton>
              <InteractiveButton
                onClick={handleContinue}
                loading={continueLoading}
                className="flex-1 rounded-xl bg-primaryGreen px-4 py-3 text-white hover:bg-green-700"
              >
                Complete Setup
              </InteractiveButton>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="flex gap-4">
                <InteractiveButton
                  type="button"
                  onClick={onBack}
                  className="flex-1 rounded-xl border border-gray-300 bg-white px-4 py-3 text-gray-700 hover:bg-gray-50"
                >
                  Back
                </InteractiveButton>
                <InteractiveButton
                  onClick={handleSkipPayment}
                  loading={skipPaymentLoading}
                  className="flex-1 rounded-xl border border-gray-300 bg-white px-4 py-3 text-gray-700 hover:bg-gray-50"
                >
                  Skip for Now
                </InteractiveButton>
              </div>
              <p className="text-center text-xs text-gray-500">
                You can set up payments later in your profile settings
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default AdminStaffBankSetup;
