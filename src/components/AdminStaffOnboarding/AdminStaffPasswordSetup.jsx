import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { InteractiveButton } from "Components/InteractiveButton";
import { FaEye, FaEyeSlash } from "react-icons/fa";

const AdminStaffPasswordSetup = ({
  onNext,
  onBack,
  register,
  setValue,
  errors,
  defaultValues,
  isSubmitting,
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const schema = yup
    .object({
      password: yup
        .string()
        .min(8, "Password must be at least 8 characters")
        .matches(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
          "Password must contain at least one uppercase letter, one lowercase letter, and one number"
        )
        .required("Password is required"),
      confirm_password: yup
        .string()
        .oneOf([yup.ref("password"), null], "Passwords must match")
        .required("Please confirm your password"),
    })
    .required();

  const {
    register: localRegister,
    handleSubmit,
    formState: { errors: localErrors },
    watch,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      password: "",
      confirm_password: "",
    },
  });

  const password = watch("password");

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const onSubmit = async (data) => {
    await onNext(data);
  };

  const getPasswordStrength = (password) => {
    if (!password) return { strength: 0, label: "", color: "" };
    
    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[^a-zA-Z\d]/.test(password)) score++;

    if (score <= 2) return { strength: score, label: "Weak", color: "bg-red-500" };
    if (score <= 3) return { strength: score, label: "Fair", color: "bg-yellow-500" };
    if (score <= 4) return { strength: score, label: "Good", color: "bg-blue-500" };
    return { strength: score, label: "Strong", color: "bg-green-500" };
  };

  const passwordStrength = getPasswordStrength(password);

  return (
    <div className="mx-auto max-w-2xl px-6">
      <div className="mb-8 text-center">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">
          Set Your Password
        </h1>
        <p className="text-gray-600">
          Create a secure password for your admin staff account
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Password */}
        <div>
          <label
            htmlFor="password"
            className="block text-sm font-medium text-gray-700"
          >
            New Password *
          </label>
          <div className="relative mt-1">
            <input
              {...localRegister("password")}
              type={showPassword ? "text" : "password"}
              id="password"
              className="block w-full rounded-xl border border-gray-300 px-3 py-3 pr-10 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"
              placeholder="Enter your new password"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center pr-3"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? (
                <FaEyeSlash className="h-5 w-5 text-gray-400" />
              ) : (
                <FaEye className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
          {localErrors.password && (
            <p className="mt-1 text-sm text-red-600">
              {localErrors.password.message}
            </p>
          )}
          
          {/* Password Strength Indicator */}
          {password && (
            <div className="mt-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Password strength:</span>
                <span className={`font-medium ${
                  passwordStrength.strength <= 2 ? 'text-red-600' :
                  passwordStrength.strength <= 3 ? 'text-yellow-600' :
                  passwordStrength.strength <= 4 ? 'text-blue-600' : 'text-green-600'
                }`}>
                  {passwordStrength.label}
                </span>
              </div>
              <div className="mt-1 h-2 w-full rounded-full bg-gray-200">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                  style={{ width: `${(passwordStrength.strength / 5) * 100}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>

        {/* Confirm Password */}
        <div>
          <label
            htmlFor="confirm_password"
            className="block text-sm font-medium text-gray-700"
          >
            Confirm Password *
          </label>
          <div className="relative mt-1">
            <input
              {...localRegister("confirm_password")}
              type={showConfirmPassword ? "text" : "password"}
              id="confirm_password"
              className="block w-full rounded-xl border border-gray-300 px-3 py-3 pr-10 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"
              placeholder="Confirm your new password"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center pr-3"
              onClick={toggleConfirmPasswordVisibility}
            >
              {showConfirmPassword ? (
                <FaEyeSlash className="h-5 w-5 text-gray-400" />
              ) : (
                <FaEye className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
          {localErrors.confirm_password && (
            <p className="mt-1 text-sm text-red-600">
              {localErrors.confirm_password.message}
            </p>
          )}
        </div>

        {/* Password Requirements */}
        <div className="rounded-lg bg-blue-50 p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">
            Password Requirements:
          </h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li className="flex items-center">
              <span className={`mr-2 ${password && password.length >= 8 ? 'text-green-600' : 'text-gray-400'}`}>
                {password && password.length >= 8 ? '✓' : '○'}
              </span>
              At least 8 characters long
            </li>
            <li className="flex items-center">
              <span className={`mr-2 ${password && /[a-z]/.test(password) ? 'text-green-600' : 'text-gray-400'}`}>
                {password && /[a-z]/.test(password) ? '✓' : '○'}
              </span>
              One lowercase letter
            </li>
            <li className="flex items-center">
              <span className={`mr-2 ${password && /[A-Z]/.test(password) ? 'text-green-600' : 'text-gray-400'}`}>
                {password && /[A-Z]/.test(password) ? '✓' : '○'}
              </span>
              One uppercase letter
            </li>
            <li className="flex items-center">
              <span className={`mr-2 ${password && /\d/.test(password) ? 'text-green-600' : 'text-gray-400'}`}>
                {password && /\d/.test(password) ? '✓' : '○'}
              </span>
              One number
            </li>
          </ul>
        </div>

        {/* Progress Indicator */}
        <div className="mt-8">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Step 2 of 3</span>
            <span>Password Setup</span>
          </div>
          <div className="mt-2 h-2 w-full rounded-full bg-gray-200">
            <div className="h-2 w-2/3 rounded-full bg-primaryGreen"></div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 pt-6">
          <InteractiveButton
            type="button"
            onClick={onBack}
            className="flex-1 rounded-xl border border-gray-300 bg-white px-4 py-3 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Back
          </InteractiveButton>
          <InteractiveButton
            type="submit"
            loading={isSubmitting}
            className="flex-1 rounded-xl bg-primaryGreen px-4 py-3 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            Continue
          </InteractiveButton>
        </div>
      </form>
    </div>
  );
};

export default AdminStaffPasswordSetup;
