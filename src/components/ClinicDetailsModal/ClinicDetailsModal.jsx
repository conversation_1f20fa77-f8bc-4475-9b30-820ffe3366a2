import React, { useState, useEffect, useContext } from "react";
import MkdSDK from "Utils/MkdSDK";
import { getManyByIds, GlobalContext, showToast } from "Context/Global";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import LoadingSpinner from "Components/LoadingSpinner";
import { convertTo12Hour, generateTimeOptions } from "Utils/utils";
import { useClub } from "Context/Club";
import Select from "react-select";
import { useForm } from "react-hook-form";
import SportChangeModal from "./SportChangeModal";
import RightSideModal from "Components/RightSideModal";
import { AuthContext } from "Context/Auth";

const timeOptions = generateTimeOptions();

let sdk = new MkdSDK();
const ClinicDetailsModal = ({ clinic, onClose, getData, isOpen }) => {
  const defaultClinic = clinic || {
    sport_id: "",
    type: "",
    sub_type: "",
    date: "",
    start_time: "",
    end_time: "",
    name: "",
    cost_per_head: "",
    description: "",
    recurring: 0,
    id: null,
  };

  // Form control - always initialize with default values
  const { setValue, watch } = useForm({
    defaultValues: {
      sport_id: defaultClinic.sport_id,
      type: defaultClinic.type,
      sub_type: defaultClinic.sub_type,
      date: defaultClinic.date,
      start_time: defaultClinic.start_time,
      end_time: defaultClinic.end_time,
      name: defaultClinic.name,
      cost_per_head: defaultClinic.cost_per_head,
      description: defaultClinic.description,
      recurring: defaultClinic.recurring,
    },
  });

  // Basic state
  const [editLoading, setEditLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [coaches, setCoaches] = useState([]);

  // Sport change modal state
  const [showSportChangeModal, setShowSportChangeModal] = useState(false);
  const [originalSportData, setOriginalSportData] = useState({
    sport_id: "",
    type: "",
    sub_type: "",
  });
  const [sportChangeOption, setSportChangeOption] = useState(null);

  // Sport, type, subtype state
  const [selectedSportTypes, setSelectedSportTypes] = useState([]);
  const [selectedSubTypes, setSelectedSubTypes] = useState([]);
  const [filteredEndTimeOptions, setFilteredEndTimeOptions] =
    useState(timeOptions);

  // Watch form values
  const watchSportId = watch("sport_id");
  const watchSportType = watch("type");
  const watchStartTime = watch("start_time");

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { sports } = useClub();

  // Initialize form values when clinic changes
  useEffect(() => {
    if (clinic) {
      setValue("sport_id", clinic.sport_id || "");
      setValue("type", clinic.type || "");
      setValue("sub_type", clinic.sub_type || "");
      setValue("date", clinic.date || "");
      setValue("start_time", clinic.start_time || "");
      setValue("end_time", clinic.end_time || "");
      setValue("name", clinic.name || "");
      setValue("cost_per_head", clinic.cost_per_head || "");
      setValue("description", clinic.description || "");
      setValue("recurring", clinic.recurring || 0);

      // Store original sport data for comparison
      setOriginalSportData({
        sport_id: clinic.sport_id || "",
        type: clinic.type || "",
        sub_type: clinic.sub_type || "",
      });
    }
  }, [clinic, setValue]);

  // Define getCoaches function before using it in useEffect
  const getCoaches = async () => {
    if (!defaultClinic.id) return [];

    setIsLoading(true);
    try {
      sdk.setTable("clinic_coaches");
      const response = await sdk.callRestAPI(
        { filter: [`clinic_id,eq,${defaultClinic.id}`] },
        "GETALL"
      );

      const coachesResponse = await getManyByIds(
        globalDispatch,
        authDispatch,
        "coach",
        response?.list.map((coach) => coach.coach_id),
        "user|user_id"
      );
      setCoaches(coachesResponse.list);
    } catch (error) {
      console.log(error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch coaches when clinic ID changes
  useEffect(() => {
    if (clinic?.id) {
      getCoaches();
    }
  }, [clinic?.id]); // Only re-fetch coaches when clinic ID changes, not on every clinic prop change

  // Update available types when sport changes
  useEffect(() => {
    if (watchSportId) {
      const selectedSport = sports.find(
        (sport) => sport.id.toString() === watchSportId.toString()
      );
      if (selectedSport) {
        // Filter out empty types (where type is an empty string)
        const validSportTypes =
          selectedSport.sport_types?.filter(
            (sportType) => sportType.type !== ""
          ) || [];
        setSelectedSportTypes(validSportTypes);
      } else {
        setSelectedSportTypes([]);
      }
    } else {
      setSelectedSportTypes([]);
    }
  }, [watchSportId, sports]);

  // Update available subtypes when type changes
  useEffect(() => {
    if (watchSportType) {
      const selectedType = selectedSportTypes.find(
        (type) => type.type === watchSportType
      );
      if (selectedType) {
        // Filter out empty subtypes
        const validSubtypes = (selectedType.subtype || []).filter(
          (subtype) => subtype !== ""
        );
        setSelectedSubTypes(validSubtypes);
      } else {
        setSelectedSubTypes([]);
      }
    } else {
      setSelectedSubTypes([]);
    }
  }, [watchSportType, selectedSportTypes]);

  // Function to filter time options based on start time
  const getFilteredTimeOptions = (startTime) => {
    if (!startTime) return timeOptions;

    // Find the index of the selected start time
    const startTimeIndex = timeOptions.findIndex(
      (option) => option.value === startTime
    );

    if (startTimeIndex === -1) return timeOptions;

    // Return only options that come after the selected start time
    return timeOptions.filter((_, index) => index > startTimeIndex);
  };

  // Update end time options when start time changes
  useEffect(() => {
    if (watchStartTime) {
      // Filter end time options to only show times after the selected start time
      const filteredOptions = getFilteredTimeOptions(watchStartTime);
      setFilteredEndTimeOptions(filteredOptions);
    } else {
      // If no start time is selected, show all options
      setFilteredEndTimeOptions(timeOptions);
    }
  }, [watchStartTime]);

  // Removed the conditional return that was here

  const refreshCoachesData = async () => {
    const updatedCoaches = await getCoaches();
    if (updatedCoaches) {
      setCoaches(updatedCoaches);
    }
  };

  // Function to handle saving all fields at once
  const handleSaveAll = async () => {
    if (!defaultClinic.id) {
      showToast(
        globalDispatch,
        "Cannot save: no clinic selected",
        3000,
        "error"
      );
      return;
    }

    setEditLoading(true);
    try {
      // Get all form values
      const formData = {
        id: defaultClinic.id,
        name: watch("name"),
        cost_per_head: parseFloat(watch("cost_per_head")),
        description: watch("description"),
        sport_id: watch("sport_id"),
        type: watch("type"),
        sub_type: watch("sub_type"),
        date: watch("date"),
        start_time: watch("start_time"),
        end_time: watch("end_time"),
        recurring:
          watch("recurring") === 1 || watch("recurring") === true ? 1 : 0,
      };

      // Check if sport/type/subtype has changed
      const sportChanged =
        formData.sport_id !== originalSportData.sport_id ||
        formData.type !== originalSportData.type ||
        formData.sub_type !== originalSportData.sub_type;

      // If sport/type/subtype changed and we have a selected option, include it in the payload
      if (sportChanged && sportChangeOption !== null) {
        formData.sport_change_option = sportChangeOption;
      }

      sdk.setTable("clinics");
      const response = await sdk.callRestAPI(formData, "PUT");

      if (!response?.error) {
        showToast(
          globalDispatch,
          "Clinic updated successfully",
          3000,
          "success"
        );

        // Update the local clinic data with all new values
        if (clinic) {
          Object.keys(formData).forEach((key) => {
            if (key !== "id") {
              clinic[key] = formData[key];
            }
          });
        }

        // Exit edit mode
        setIsEditing(false);

        // Refresh coaches data
        await refreshCoachesData();

        // Refresh the data in the parent component
        getData();
      }
    } catch (error) {
      showToast(
        globalDispatch,
        error?.message || "An error occurred",
        3000,
        "error"
      );
      console.log(error);
    } finally {
      setEditLoading(false);
    }
  };

  // Make sure form values are updated when entering edit mode
  useEffect(() => {
    if (isEditing) {
      // Ensure all form values are set correctly
      setValue("name", defaultClinic.name || "");
      setValue("cost_per_head", defaultClinic.cost_per_head || "");
      setValue("description", defaultClinic.description || "");
      setValue("sport_id", defaultClinic.sport_id || "");
      setValue("type", defaultClinic.type || "");
      setValue("sub_type", defaultClinic.sub_type || "");
      setValue("date", defaultClinic.date || "");
      setValue("start_time", defaultClinic.start_time || "");
      setValue("end_time", defaultClinic.end_time || "");
      setValue("recurring", defaultClinic.recurring);
    }
  }, [isEditing, defaultClinic, setValue]);

  // Don't render anything if not open
  if (!isOpen) {
    return null;
  }

  return (
    <>
      <RightSideModal
        isOpen={isOpen}
        onClose={onClose}
        title={defaultClinic.name || "Clinic details"}
        showFooter={false}
      >
        <div className="space-y-6">
          {/* Header with clinic info */}
          <div className="rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {defaultClinic.name || "Clinic Details"}
                </h3>
                <p className="text-sm text-gray-600">
                  {sports.find((s) => s.id.toString() == defaultClinic.sport_id)
                    ?.name || "No sport selected"}
                  {defaultClinic.type && ` • ${defaultClinic.type}`}
                  {defaultClinic.sub_type && ` • ${defaultClinic.sub_type}`}
                </p>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-primaryBlue">
                  {fCurrency(defaultClinic.cost_per_head)}
                </div>
                <div className="text-sm text-gray-500">per person</div>
              </div>
            </div>
          </div>

          {/* Edit/Save/Cancel buttons */}
          <div className="flex justify-end border-b border-gray-200 pb-4">
            {isEditing ? (
              <div className="flex gap-3">
                <button
                  onClick={() => setIsEditing(false)}
                  className="rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <InteractiveButton
                  loading={editLoading}
                  onClick={handleSaveAll}
                  className="rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md"
                >
                  {editLoading ? "Saving..." : "Save All Changes"}
                </InteractiveButton>
              </div>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md"
              >
                <div className="flex items-center space-x-2">
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                  <span>Edit Details</span>
                </div>
              </button>
            )}
          </div>

          {/* Basic Information Section */}
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-2">
              <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-900">
                Basic Information
              </h4>
            </div>

            {/* Name */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Clinic Name
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={watch("name") || ""}
                  onChange={(e) => setValue("name", e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                  placeholder="Enter clinic name"
                />
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {defaultClinic.name || "No name provided"}
                </div>
              )}
            </div>

            {/* Cost per person */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Cost per Person
              </label>
              {isEditing ? (
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                    <span className="font-medium text-gray-500">$</span>
                  </div>
                  <input
                    type="number"
                    value={watch("cost_per_head") || ""}
                    onChange={(e) => setValue("cost_per_head", e.target.value)}
                    className="w-full rounded-lg border border-gray-300 py-2 pl-8 pr-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                </div>
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {fCurrency(defaultClinic.cost_per_head)}
                </div>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Description
              </label>
              {isEditing ? (
                <textarea
                  value={watch("description") || ""}
                  onChange={(e) => setValue("description", e.target.value)}
                  className="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                  rows={3}
                  placeholder="Enter clinic description"
                />
              ) : (
                <div className="min-h-[80px] rounded-lg bg-gray-50 px-3 py-2 text-gray-900">
                  {defaultClinic.description || "No description provided"}
                </div>
              )}
            </div>
          </div>

          {/* Sport Configuration Section */}
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-2">
              <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-900">
                Sport Configuration
              </h4>
            </div>

            {/* Sport */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Sport</label>
              {isEditing ? (
                <Select
                  className="w-full text-sm"
                  options={sports
                    .filter((sport) => sport.status === 1)
                    .map((sport) => ({
                      value: sport.id.toString(),
                      label: sport.name,
                    }))}
                  value={{
                    value: watchSportId,
                    label:
                      sports.find((s) => s.id.toString() == watchSportId)
                        ?.name || "Select sport",
                  }}
                  onChange={(option) => {
                    // Check if sport has changed
                    if (option.value !== originalSportData.sport_id) {
                      // Show confirmation modal
                      setShowSportChangeModal(true);
                    } else {
                      // If same sport, just update the value
                      setValue("sport_id", option.value);
                      setValue("type", "");
                      setValue("sub_type", "");
                    }
                  }}
                  styles={{
                    control: (base) => ({
                      ...base,
                      borderRadius: "0.5rem",
                      borderColor: "#d1d5db",
                      "&:hover": { borderColor: "#9ca3af" },
                      "&:focus-within": {
                        borderColor: "#3b82f6",
                        boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
                      },
                    }),
                  }}
                />
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {sports.find((s) => s.id.toString() == defaultClinic.sport_id)
                    ?.name || "No sport selected"}
                </div>
              )}
            </div>

            {/* Type */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Type</label>
              {isEditing ? (
                <>
                  {selectedSportTypes.length > 0 ? (
                    <Select
                      className="w-full text-sm"
                      options={selectedSportTypes.map((type) => ({
                        value: type.type,
                        label: type.type,
                      }))}
                      value={{
                        value: watchSportType,
                        label: watchSportType || "Select type",
                      }}
                      onChange={(option) => {
                        // Check if type has changed and sport is the same
                        if (
                          option.value !== originalSportData.type &&
                          watchSportId === originalSportData.sport_id
                        ) {
                          // Show confirmation modal
                          setShowSportChangeModal(true);
                        } else {
                          // If same type or sport already changed, just update the value
                          setValue("type", option.value);
                          setValue("sub_type", "");
                        }
                      }}
                      styles={{
                        control: (base) => ({
                          ...base,
                          borderRadius: "0.5rem",
                          borderColor: "#d1d5db",
                          "&:hover": { borderColor: "#9ca3af" },
                          "&:focus-within": {
                            borderColor: "#3b82f6",
                            boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
                          },
                        }),
                      }}
                    />
                  ) : (
                    <div className="rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500">
                      This sport has no types
                    </div>
                  )}
                </>
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {defaultClinic.type || "No type selected"}
                </div>
              )}
            </div>

            {/* Sub-type */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Sub-type
              </label>
              {isEditing ? (
                <>
                  {selectedSubTypes.length > 0 ? (
                    <Select
                      className="w-full text-sm"
                      options={selectedSubTypes.map((subtype) => ({
                        value: subtype,
                        label: subtype,
                      }))}
                      value={{
                        value: watch("sub_type"),
                        label: watch("sub_type") || "Select sub-type",
                      }}
                      onChange={(option) => {
                        // Check if subtype has changed and sport/type are the same
                        if (
                          option.value !== originalSportData.sub_type &&
                          watchSportId === originalSportData.sport_id &&
                          watchSportType === originalSportData.type
                        ) {
                          // Show confirmation modal
                          setShowSportChangeModal(true);
                        } else {
                          // If same subtype or sport/type already changed, just update the value
                          setValue("sub_type", option.value);
                        }
                      }}
                      styles={{
                        control: (base) => ({
                          ...base,
                          borderRadius: "0.5rem",
                          borderColor: "#d1d5db",
                          "&:hover": { borderColor: "#9ca3af" },
                          "&:focus-within": {
                            borderColor: "#3b82f6",
                            boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
                          },
                        }),
                      }}
                    />
                  ) : (
                    <div className="rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500">
                      This type has no sub-types
                    </div>
                  )}
                </>
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {defaultClinic.sub_type || "No sub-type selected"}
                </div>
              )}
            </div>
          </div>

          {/* Scheduling Section */}
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-2">
              <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-900">
                Scheduling
              </h4>
            </div>

            {/* Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Date</label>
              {isEditing ? (
                <input
                  type="date"
                  value={watch("date") || ""}
                  onChange={(e) => setValue("date", e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                />
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {defaultClinic.date
                    ? new Date(defaultClinic.date).toLocaleDateString("en-US", {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })
                    : "No date set"}
                </div>
              )}
            </div>

            {/* Time Range */}
            <div className="grid grid-cols-2 gap-4">
              {/* Start time */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Start Time
                </label>
                {isEditing ? (
                  <Select
                    className="w-full text-sm"
                    options={timeOptions}
                    value={{
                      value: watch("start_time"),
                      label:
                        convertTo12Hour(watch("start_time")) || "Select time",
                    }}
                    onChange={(option) => {
                      setValue("start_time", option.value);
                    }}
                    placeholder="Select start time"
                    styles={{
                      control: (base) => ({
                        ...base,
                        borderRadius: "0.5rem",
                        borderColor: "#d1d5db",
                        "&:hover": { borderColor: "#9ca3af" },
                        "&:focus-within": {
                          borderColor: "#3b82f6",
                          boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
                        },
                      }),
                    }}
                  />
                ) : (
                  <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                    {convertTo12Hour(defaultClinic.start_time) || "Not set"}
                  </div>
                )}
              </div>

              {/* End time */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  End Time
                </label>
                {isEditing ? (
                  <Select
                    className="w-full text-sm"
                    options={filteredEndTimeOptions}
                    value={{
                      value: watch("end_time"),
                      label:
                        convertTo12Hour(watch("end_time")) || "Select time",
                    }}
                    onChange={(option) => {
                      setValue("end_time", option.value);
                    }}
                    placeholder={
                      !watchStartTime
                        ? "Select start time first"
                        : "Select end time"
                    }
                    isDisabled={!watchStartTime}
                    styles={{
                      control: (base, state) => ({
                        ...base,
                        borderRadius: "0.5rem",
                        borderColor: state.isDisabled ? "#e5e7eb" : "#d1d5db",
                        backgroundColor: state.isDisabled ? "#f9fafb" : "white",
                        "&:hover": {
                          borderColor: state.isDisabled ? "#e5e7eb" : "#9ca3af",
                        },
                        "&:focus-within": {
                          borderColor: "#3b82f6",
                          boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
                        },
                      }),
                    }}
                  />
                ) : (
                  <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                    {convertTo12Hour(defaultClinic.end_time) || "Not set"}
                  </div>
                )}
              </div>
            </div>

            {/* Recurring */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Recurring Event
              </label>
              {isEditing ? (
                <select
                  value={
                    watch("recurring") === 1 || watch("recurring") === true
                      ? "Yes"
                      : "No"
                  }
                  onChange={(e) =>
                    setValue("recurring", e.target.value === "Yes" ? 1 : 0)
                  }
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                >
                  <option value="No">No</option>
                  <option value="Yes">Yes</option>
                </select>
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  <div className="flex items-center space-x-2">
                    <div
                      className={`h-2 w-2 rounded-full ${
                        defaultClinic.recurring === 1
                          ? "bg-green-400"
                          : "bg-gray-400"
                      }`}
                    ></div>
                    <span>{defaultClinic.recurring === 1 ? "Yes" : "No"}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Coaches Section */}
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-900">
                  Coaches
                </h4>
                <span className="rounded-full bg-primaryBlue/10 px-2 py-1 text-xs font-semibold text-primaryBlue">
                  {coaches.length} assigned
                </span>
              </div>
            </div>

            {coaches.length > 0 ? (
              <div className="space-y-3">
                {coaches.map((coach) => (
                  <div
                    key={coach.id}
                    className="flex items-center space-x-3 rounded-lg border border-gray-200 bg-gray-50 p-3 transition-all hover:shadow-sm"
                  >
                    <div className="h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white">
                      <img
                        src={
                          coach.user?.photo ||
                          coach.photo ||
                          "/default-avatar.png"
                        }
                        alt={`${coach.user?.first_name || ""} ${
                          coach.user?.last_name || ""
                        }`}
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          e.target.src = "/default-avatar.png";
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold text-gray-900">
                        {coach.user?.first_name} {coach.user?.last_name}
                      </div>
                      {coach.user?.email && (
                        <div className="text-sm text-gray-500">
                          {coach.user.email}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="h-2 w-2 rounded-full bg-green-400"></div>
                      <span className="text-xs text-gray-500">Active</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="rounded-lg border-2 border-dashed border-gray-200 p-6 text-center">
                <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                  <svg
                    className="h-6 w-6 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"
                    />
                  </svg>
                </div>
                <p className="font-medium text-gray-500">No coaches assigned</p>
                <p className="mt-1 text-sm text-gray-400">
                  Coaches will appear here when assigned to this clinic
                </p>
              </div>
            )}
          </div>

          {isLoading && <LoadingSpinner />}
        </div>
      </RightSideModal>{" "}
      {/* Render the sport change modal when needed */}
      {showSportChangeModal && (
        <SportChangeModal
          onClose={() => {
            // Reset to original values
            setValue("sport_id", originalSportData.sport_id);
            setValue("type", originalSportData.type);
            setValue("sub_type", originalSportData.sub_type);

            // Reset option and close modal
            setSportChangeOption(null);
            setShowSportChangeModal(false);
          }}
          onConfirm={(option) => {
            // Set the selected option in the parent component
            setSportChangeOption(parseInt(option));

            // Apply the sport/type/subtype changes
            const newSportId = watch("sport_id");
            const newType = watch("type");
            const newSubType = watch("sub_type");

            // Update form values
            setValue("sport_id", newSportId);
            setValue("type", newType);
            setValue("sub_type", newSubType);

            // Close the modal
            setShowSportChangeModal(false);
          }}
          eventCounts={{
            total: 12, // Example data - in a real app, this would be fetched from the API
            completed: 4,
            upcoming: "April 3, 2025", // Next upcoming event date
            lastEvent: "June 19, 2025", // Last event date
          }}
        />
      )}
    </>
  );
};

export default ClinicDetailsModal;
