import React, { useEffect, useState, useRef } from "react";
import { GlobalContext } from "Context/Global";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { BackButton } from "Components/BackButton";
import { AuthContext } from "Context/Auth";
import { useProfile } from "Hooks/useProfile";
import { NAV_ITEMS } from "Components/ClubHeader/ClubHeader";
import DownloadAppModal from "Components/DownloadAppModal";
import { ChevronDownIcon } from "@heroicons/react/24/solid";
import SupportChatBot from "Components/SupportChat/SupportChatBot";
import { IoClose } from "react-icons/io5";

const TopHeader = () => {
  const { state: globalState } = React.useContext(GlobalContext);
  const [isDownloadAppModalOpen, setIsDownloadAppModalOpen] =
    React.useState(false);
  const [showMobilePromo, setShowMobilePromo] = React.useState(true);
  const handleCloseMobilePromo = () => {
    setShowMobilePromo(false);
  };

  const navigate = useNavigate();
  const { dispatch } = React.useContext(AuthContext);
  const { showBackButton } = globalState;
  const location = useLocation();
  const [profile] = useProfile();
  const [currentPageInfo, setCurrentPageInfo] = useState({
    text: "",
    icon: null,
  });
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef(null);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    // Find matching nav item based on current path
    const currentPath = location.pathname;
    const matchingNavItem = NAV_ITEMS.find((item) =>
      currentPath.includes(item.to)
    );

    if (matchingNavItem) {
      setCurrentPageInfo({
        text: matchingNavItem.text,
        icon: matchingNavItem.icon,
      });
    }
  }, [location]);
  const userRole = localStorage.getItem("role");
  const userMenu = [
    {
      text: "Profile details",
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4.88083 15.7631C5.97548 14.164 7.77671 13.125 10 13.125C12.2233 13.125 14.0246 14.164 15.1192 15.7631M4.88083 15.7631C6.2422 16.9732 8.03527 17.7083 10 17.7083C11.9648 17.7083 13.7578 16.9732 15.1192 15.7631M4.88083 15.7631C3.2924 14.3511 2.29169 12.2924 2.29169 9.99996C2.29169 5.74276 5.74283 2.29163 10 2.29163C14.2572 2.29163 17.7084 5.74276 17.7084 9.99996C17.7084 12.2924 16.7076 14.3511 15.1192 15.7631M12.7084 8.33329C12.7084 9.82906 11.4958 11.0416 10 11.0416C8.50425 11.0416 7.29169 9.82906 7.29169 8.33329C7.29169 6.83752 8.50425 5.62496 10 5.62496C11.4958 5.62496 12.7084 6.83752 12.7084 8.33329Z"
            stroke="#868C98"
            stroke-width="1.5"
            stroke-linejoin="round"
          />
        </svg>
      ),
      to: `/user/profile`,
    },
    {
      text: "Payment methods",
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2.29169 8.12496V15.205C2.29169 15.6653 2.66478 16.0384 3.12502 16.0384L16.8718 16.0384C17.332 16.0384 17.7051 15.6653 17.7051 15.205V8.12496M2.29169 8.12496V4.79403C2.29169 4.33379 2.66478 3.96069 3.12502 3.96069H16.8709C17.3302 3.96069 17.703 4.33231 17.7037 4.79161C17.7056 5.90272 17.7051 7.01384 17.7051 8.12496M2.29169 8.12496H17.7051M5.62502 11.0416H8.12502"
            stroke="#868C98"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      ),
      to: `/user/profile?tab=payment-methods`,
    },
    {
      text: "Membership",
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6.45831 12.4999V18.0933C6.45831 18.3988 6.77604 18.6004 7.05239 18.4704L9.82256 17.1667C9.93493 17.1139 10.065 17.1139 10.1774 17.1667L12.9476 18.4704C13.2239 18.6004 13.5416 18.3988 13.5416 18.0933V12.4999M16.0416 7.49992C16.0416 10.8366 13.3367 13.5416 9.99998 13.5416C6.66326 13.5416 3.95831 10.8366 3.95831 7.49992C3.95831 4.1632 6.66326 1.45825 9.99998 1.45825C13.3367 1.45825 16.0416 4.1632 16.0416 7.49992Z"
            stroke="#868C98"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      ),
      to: `/user/profile?tab=membership`,
    },
    {
      text: "Billing ",
      icon: (
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.00002 5.14591V4.28943M9.00002 12.8542V13.7107M10.8548 6.43064C10.4845 5.91863 9.79257 5.57416 9.00002 5.57416H8.76211C7.71095 5.57416 6.85882 6.25586 6.85882 7.09679V7.16216C6.85882 7.76365 7.28361 8.31352 7.9561 8.58251L10.0439 9.41765C10.7164 9.68665 11.1412 10.2365 11.1412 10.838C11.1412 11.715 10.2525 12.426 9.15622 12.426H9.00002C8.20747 12.426 7.5155 12.0815 7.14527 11.5695M16.7084 9.00008C16.7084 13.2573 13.2572 16.7084 9.00002 16.7084C4.74283 16.7084 1.29169 13.2573 1.29169 9.00008C1.29169 4.74289 4.74283 1.29175 9.00002 1.29175C13.2572 1.29175 16.7084 4.74289 16.7084 9.00008Z"
            stroke="#868C98"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      ),
      to: `/user/profile?tab=billing`,
    },
    {
      text: "Get the app",
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.95835 3.54159H11.0417M5.62502 18.5416H14.375C14.8353 18.5416 15.2084 18.1685 15.2084 17.7083V2.29159C15.2084 1.83135 14.8353 1.45825 14.375 1.45825H5.62502C5.16478 1.45825 4.79169 1.83135 4.79169 2.29159V17.7083C4.79169 18.1685 5.16478 18.5416 5.62502 18.5416Z"
            stroke="#868C98"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      ),
      onClick: () => setIsDownloadAppModalOpen(true),
    },
    {
      text: "Logout",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M13.3333 5.41666C13.3333 7.25761 11.8409 8.74999 9.99997 8.74999C8.15902 8.74999 6.66664 7.25761 6.66664 5.41666C6.66664 3.57571 8.15902 2.08332 9.99997 2.08332C11.8409 2.08332 13.3333 3.57571 13.3333 5.41666Z"
            fill="#A8A8A8"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.99997 2.49999C8.38914 2.49999 7.08331 3.80583 7.08331 5.41666C7.08331 7.02749 8.38914 8.33332 9.99997 8.33332C11.6108 8.33332 12.9166 7.02749 12.9166 5.41666C12.9166 3.80583 11.6108 2.49999 9.99997 2.49999ZM6.24997 5.41666C6.24997 3.34559 7.9289 1.66666 9.99997 1.66666C12.071 1.66666 13.75 3.34559 13.75 5.41666C13.75 7.48772 12.071 9.16666 9.99997 9.16666C7.9289 9.16666 6.24997 7.48772 6.24997 5.41666Z"
            fill="#A8A8A8"
          />
          <path
            d="M9.99997 10.4167C6.27535 10.4167 3.66126 13.3457 3.33331 17.0833H16.6666C16.3387 13.3457 13.7246 10.4167 9.99997 10.4167Z"
            fill="#A8A8A8"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M3.80032 16.6667H16.1996C15.725 13.323 13.3164 10.8333 9.99997 10.8333C6.68352 10.8333 4.27494 13.323 3.80032 16.6667ZM2.91823 17.0469C3.26095 13.1409 6.01533 9.99999 9.99997 9.99999C13.9846 9.99999 16.739 13.1409 17.0817 17.0469L17.1215 17.5H2.87848L2.91823 17.0469Z"
            fill="#A8A8A8"
          />
        </svg>
      ),
      to: `/user/login`,
      onClick: () => {
        dispatch({
          type: "LOGOUT",
        });
      },
    },
  ];

  return (
    <div className="relative">
      {isDownloadAppModalOpen && (
        <DownloadAppModal onClose={() => setIsDownloadAppModalOpen(false)} />
      )}
      <div className="sticky top-0 z-50 flex h-14 w-full items-center justify-between border-b border-gray-200 bg-white px-6 py-8 ">
        <div className="flex items-center gap-3">
          {showBackButton && <BackButton />}
          <div className="flex items-center gap-2">
            {currentPageInfo.icon}
            <p className="text-lg font-medium text-gray-700">
              {currentPageInfo.text}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 pr-2">
          {userRole === "user" && (
            <button
              onClick={() => navigate("/user/reserve-court")}
              className="hidden items-center gap-2 whitespace-nowrap rounded-xl bg-navy-700 px-3.5 py-1.5 text-sm text-white sm:flex"
            >
              <span className="text-lg">+</span>
              Reserve a court
            </button>
          )}
          <div ref={menuRef} className="relative">
            <button className="flex items-center gap-2" onClick={toggleMenu}>
              <div title="Profile" className="peer">
                <img
                  className="h-[30px] w-[30px] rounded-full object-cover"
                  src={profile?.photo || "/default-avatar.png"}
                  alt={`${profile?.first_name} ${profile?.last_name}`}
                />
              </div>
              <p className="hidden max-w-[120px] truncate text-sm font-medium text-black sm:block">
                {profile?.first_name && profile?.last_name
                  ? `${profile?.first_name} ${profile?.last_name}`
                  : "Profile"}
              </p>
              <ChevronDownIcon className="h-4 w-4" />
            </button>
            <ul
              className={`absolute right-0 top-[100%] z-20 mt-2 sm:right-5 ${
                isMenuOpen ? "block" : "hidden"
              } min-w-[200px] rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-[#525252] shadow-lg`}
            >
              {(userRole == "user" &&
                userMenu.map((item) => (
                  <button
                    className="hover:text[#262626] flex w-full cursor-pointer items-center rounded-md px-2 py-3 hover:bg-[#F4F4F4]"
                    key={item.text}
                    onClick={() => {
                      if (item.onClick && typeof item.onClick === "function") {
                        item.onClick();
                      }
                      if (item.to && typeof item.to === "string") {
                        navigate(item.to);
                      }
                    }}
                  >
                    <div className="mr-2">{item.icon}</div>
                    <span className="truncate">{item.text}</span>
                  </button>
                ))) || (
                <>
                  <li>
                    <Link
                      className="hover:text[#262626] flex cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4]"
                      to={`/${profile?.role}/profile`}
                    >
                      <span className="mr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                        >
                          <path
                            d="M13.3333 5.41666C13.3333 7.25761 11.8409 8.74999 9.99997 8.74999C8.15902 8.74999 6.66664 7.25761 6.66664 5.41666C6.66664 3.57571 8.15902 2.08332 9.99997 2.08332C11.8409 2.08332 13.3333 3.57571 13.3333 5.41666Z"
                            fill="#A8A8A8"
                          />
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M9.99997 2.49999C8.38914 2.49999 7.08331 3.80583 7.08331 5.41666C7.08331 7.02749 8.38914 8.33332 9.99997 8.33332C11.6108 8.33332 12.9166 7.02749 12.9166 5.41666C12.9166 3.80583 11.6108 2.49999 9.99997 2.49999ZM6.24997 5.41666C6.24997 3.34559 7.9289 1.66666 9.99997 1.66666C12.071 1.66666 13.75 3.34559 13.75 5.41666C13.75 7.48772 12.071 9.16666 9.99997 9.16666C7.9289 9.16666 6.24997 7.48772 6.24997 5.41666Z"
                            fill="#A8A8A8"
                          />
                          <path
                            d="M9.99997 10.4167C6.27535 10.4167 3.66126 13.3457 3.33331 17.0833H16.6666C16.3387 13.3457 13.7246 10.4167 9.99997 10.4167Z"
                            fill="#A8A8A8"
                          />
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M3.80032 16.6667H16.1996C15.725 13.323 13.3164 10.8333 9.99997 10.8333C6.68352 10.8333 4.27494 13.323 3.80032 16.6667ZM2.91823 17.0469C3.26095 13.1409 6.01533 9.99999 9.99997 9.99999C13.9846 9.99999 16.739 13.1409 17.0817 17.0469L17.1215 17.5H2.87848L2.91823 17.0469Z"
                            fill="#A8A8A8"
                          />
                        </svg>
                      </span>
                      <span>Account</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      className="hover:text[#262626] group flex cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4] hover:text-red-500"
                      to={`/${
                        profile?.role == "admin_staff"
                          ? "admin-staff"
                          : profile?.role
                      }/login`}
                      onClick={() =>
                        dispatch({
                          type: "LOGOUT",
                        })
                      }
                    >
                      <span className="mr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                        >
                          <path
                            className="group-hover:fill-[#ef4444] group-hover:stroke-[#ef4444]"
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M3.75 3.33333C3.51988 3.33333 3.33333 3.51988 3.33333 3.75L3.33333 16.25C3.33333 16.4801 3.51988 16.6667 3.75 16.6667H9.58333C9.81345 16.6667 10 16.8532 10 17.0833C10 17.3135 9.81345 17.5 9.58333 17.5H3.75C3.05964 17.5 2.5 16.9404 2.5 16.25L2.5 3.75C2.5 3.05964 3.05964 2.5 3.75 2.5L9.58333 2.5C9.81345 2.5 10 2.68655 10 2.91667C10 3.14679 9.81345 3.33333 9.58333 3.33333L3.75 3.33333ZM13.0387 5.95537C13.2014 5.79265 13.4652 5.79265 13.628 5.95537L17.378 9.70536C17.5407 9.86808 17.5407 10.1319 17.378 10.2946L13.628 14.0446C13.4652 14.2073 13.2014 14.2073 13.0387 14.0446C12.876 13.8819 12.876 13.6181 13.0387 13.4554L16.0774 10.4167L7.91667 10.4167C7.68655 10.4167 7.5 10.2301 7.5 9.99999C7.5 9.76987 7.68655 9.58332 7.91667 9.58332L16.0774 9.58332L13.0387 6.54463C12.876 6.38191 12.876 6.11809 13.0387 5.95537Z"
                            fill="#A8A8A8"
                            stroke="#A8A8A8"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </span>
                      <span>Logout</span>
                    </Link>
                  </li>
                </>
              )}
            </ul>
          </div>
        </div>
      </div>

      {/* Mobile App Promo */}
      {userRole == "user" && <SupportChatBot />}
    </div>
  );
};

export default TopHeader;
