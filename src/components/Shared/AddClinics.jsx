import React, { useContext, useEffect, useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { BackButton } from "Components/BackButton";
import RightSideModal from "Components/RightSideModal";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { Calendar } from "Components/Calendar";
import TimeSlots from "Components/TimeSlots/TimeSlots";
import {
  generateTimeSlots,
  getTimeRange,
  logActivity,
  actionLogTypes,
  activityLogTypes,
} from "Utils/utils";
import SportTypeSelection from "./SportTypeSelection";
import { InteractiveButton } from "Components/InteractiveButton";
import moment from "moment";
import CoachSelectionModal from "Components/CoachSelectionModal";

let sdk = new MkdSDK();

export default function AddClinics({ club, coaches, role, sports }) {
  const [currentStep, setCurrentStep] = useState("selection"); // 'selection' or 'details'
  const [selectedSport, setSelectedSport] = useState(null);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedSubType, setSelectedSubType] = useState(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedEndDate, setSelectedEndDate] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTimes, setSelectedTimes] = useState([]);
  const navigate = useNavigate();
  const user_id = localStorage.getItem("user");

  // Form states
  const [clinicName, setClinicName] = useState("");
  const [numberOfPlayers, setNumberOfPlayers] = useState("");
  const [description, setDescription] = useState("");
  const [pricePerPerson, setPricePerPerson] = useState("0.00");

  const [isCoachModalOpen, setIsCoachModalOpen] = useState(false);
  const [selectedCoaches, setSelectedCoaches] = useState([]);

  const [isHoursModalOpen, setIsHoursModalOpen] = useState(false);
  const [selectedCoachForHours, setSelectedCoachForHours] = useState({
    id: null,
    hours: [],
  });

  const { dispatch: globalDispatch } = useContext(GlobalContext);

  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
    );
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    );
  };
  // console.log({ timeRange });
  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Validate that all coaches have hours set
      const { end_time, start_time } = getTimeRange(selectedTimes);
      const coachesWithoutHours = selectedCoaches.filter(
        (coach) => !coach.hours?.length
      );
      if (coachesWithoutHours.length > 0) {
        const coachNames = coachesWithoutHours
          .map((coach) => `${coach.user?.first_name} ${coach.user?.last_name}`)
          .join(", ");
        throw new Error(
          `Please set hours for the following coaches: ${coachNames}`
        );
      }

      // Format coach data for submission - include both hours array and start/end times
      const formattedCoaches = selectedCoaches.map((coach) => ({
        coach_id: coach.id,
        hours: coach.hours, // Keep original hours array
        start_time: coach.hours[0], // First selected hour
        end_time: coach.hours[coach.hours.length - 1], // Last selected hour
      }));

      const clinicFormData = {
        name: clinicName,
        max_participants: parseInt(numberOfPlayers),
        description,
        cost_per_head: parseFloat(pricePerPerson),
        date: moment(selectedDate).format("YYYY-MM-DD"), // Format: YYYY-MM-DD
        end_date: selectedEndDate
          ? moment(selectedEndDate).format("YYYY-MM-DD")
          : null,
        sport_id: parseInt(selectedSport),
        sub_type: selectedSubType,
        type: selectedType,
        start_time: start_time,
        end_time: end_time,
        club_id: club?.id,
      };

      // Validate required fields
      const requiredFields = {
        name: clinicName,
        max_participants: numberOfPlayers,
        cost_per_head: pricePerPerson,
        date: moment(selectedDate).format("YYYY-MM-DD"),
        sport_id: selectedSport,
        coaches: selectedCoaches,
      };

      const emptyFields = Object.entries(requiredFields)
        .filter(
          ([_, value]) => !value || (Array.isArray(value) && !value.length)
        )
        .map(([key]) => key.replace(/_/g, " "));

      if (emptyFields.length > 0) {
        throw new Error(
          `Please fill in the following required fields: ${emptyFields.join(
            ", "
          )}`
        );
      }

      console.log("Submitting clinic data:", clinicFormData);

      sdk.setTable("clinics");
      const response = await sdk.callRestAPI(clinicFormData, "POST");

      if (!response.error) {
        formattedCoaches.forEach(async (coach) => {
          const clinicCoachFormData = {
            clinic_id: response.data,
            coach_id: coach.coach_id,
            data: JSON.stringify({
              working_hours: coach.hours,
              fees: pricePerPerson,
              sport_id: parseInt(selectedSport),
              sub_type: selectedSubType,
              type: selectedType,
              court_ids: "",
              number_of_players: numberOfPlayers,
            }),
          };
          sdk.setTable("clinic_coaches");
          await sdk.callRestAPI(clinicCoachFormData, "POST");
        });
        await logActivity(sdk, {
          user_id: user_id,
          activity_type: activityLogTypes.clinic,
          action_type: actionLogTypes.CREATE,
          data: clinicFormData,
          club_id: club?.id,
          description: `Created a clinic`,
        });

        showToast(
          globalDispatch,
          "Clinic created successfully",
          3000,
          "success"
        );
        navigate(
          role === "club" ? "/club/program-clinics" : "/admin/program-clinics"
        );
      } else {
        showToast(
          globalDispatch,
          response.message || "Failed to create clinic",
          3000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error submitting clinic:", error);
      showToast(globalDispatch, error.message, 3000, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "program-clinics",
      },
    });
  }, [club]);

  const handleTimeClick = (time) => {
    setSelectedTimes([
      {
        from: time.from,
        until: time.until,
      },
    ]);
  };

  const HoursSelectionModal = () => {
    const { duration, end_time, start_time } = getTimeRange(selectedTimes);

    // Helper function to convert 24-hour time to comparable number
    const timeToNumber = (time24) => {
      if (!time24) return 0;
      const [hours, minutes] = time24.split(":").map(Number);
      return hours * 60 + minutes;
    };

    // Helper function to format 24-hour time to 12-hour format
    const formatTo12Hour = (time24) => {
      if (!time24) return "";
      const [hours, minutes] = time24.split(":").map(Number);
      const period = hours >= 12 ? "PM" : "AM";
      const hour12 = hours % 12 || 12;
      return `${hour12}:${minutes.toString().padStart(2, "0")} ${period}`;
    };

    // Sort function for time strings in 24-hour format
    const sortTimes = (times) => {
      return [...times].sort((a, b) => timeToNumber(a) - timeToNumber(b));
    };

    const clinicStartTime = formatTo12Hour(start_time);
    const clinicEndTime = formatTo12Hour(end_time);

    // Generate array of time slots between start_time and end_time
    const generateTimeSlotsBetween = (start, end) => {
      const slots = [];
      const startMinutes = timeToNumber(start);
      const endMinutes = timeToNumber(end);

      for (let minutes = startMinutes; minutes <= endMinutes; minutes += 30) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        const time24 = `${hours.toString().padStart(2, "0")}:${mins
          .toString()
          .padStart(2, "0")}:00`;
        slots.push({
          time24,
          time12: formatTo12Hour(time24),
        });
      }
      return slots;
    };

    const timeSlots = generateTimeSlotsBetween(start_time, end_time);

    // Initialize selected hours from the coach's existing hours
    const [selectedHours, setSelectedHours] = useState(
      selectedCoachForHours?.hours?.filter((hour) => {
        if (!start_time || !end_time) return true;
        const hourTime = hour.split(" ")[0] + ":00"; // Convert to 24-hour format
        return (
          timeToNumber(hourTime) >= timeToNumber(start_time) &&
          timeToNumber(hourTime) <= timeToNumber(end_time)
        );
      }) || []
    );

    const toggleTimeSlot = (timeObj) => {
      setSelectedHours((prev) =>
        prev.includes(timeObj.time12)
          ? prev.filter((t) => t !== timeObj.time12)
          : sortTimes([...prev, timeObj.time12])
      );
    };

    const handleSave = () => {
      // Validate that all selected hours are within clinic time range
      const invalidHours = selectedHours.filter((hour) => {
        // Convert selected hour to 24-hour format
        const [time, period] = hour.split(" ");
        const [hours, minutes] = time.split(":").map(Number);
        let hour24 = hours;

        if (period === "PM" && hours !== 12) {
          hour24 = hours + 12;
        } else if (period === "AM" && hours === 12) {
          hour24 = 0;
        }

        const hourTime = `${hour24.toString().padStart(2, "0")}:${minutes
          .toString()
          .padStart(2, "0")}:00`;

        return (
          timeToNumber(hourTime) < timeToNumber(start_time) ||
          timeToNumber(hourTime) > timeToNumber(end_time)
        );
      });

      if (invalidHours.length > 0) {
        showToast(
          globalDispatch,
          `Selected hours must be between ${clinicStartTime} and ${clinicEndTime}`,
          3000,
          "error"
        );
        return;
      }

      setSelectedCoaches((prev) =>
        prev.map((coach) =>
          coach.id === selectedCoachForHours.id
            ? { ...coach, hours: selectedHours }
            : coach
        )
      );
      setIsHoursModalOpen(false);
    };

    return (
      <RightSideModal
        isOpen={isHoursModalOpen}
        onClose={() => setIsHoursModalOpen(false)}
        title="Set Coach Hours"
        primaryButtonText="Save Hours"
        onPrimaryAction={handleSave}
      >
        <div className="flex flex-col space-y-6">
          {/* Coach Info Header */}
          <div className="flex items-center space-x-3 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
            <div className="h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-white">
              <img
                src={
                  selectedCoachForHours?.user?.photo || "/default-avatar.png"
                }
                alt={`${selectedCoachForHours?.user?.first_name} ${selectedCoachForHours?.user?.last_name}`}
                className="h-full w-full object-cover"
              />
            </div>
            <div>
              <p className="font-semibold text-gray-900">
                {selectedCoachForHours?.user?.first_name}{" "}
                {selectedCoachForHours?.user?.last_name}
              </p>
              <p className="text-sm text-gray-600">Select available hours</p>
            </div>
          </div>

          {/* Clinic time range info */}
          {clinicStartTime && clinicEndTime && (
            <div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
              <div className="flex items-center space-x-2">
                <svg
                  className="h-5 w-5 text-amber-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="font-medium text-amber-800">
                  Clinic Duration: {clinicStartTime} - {clinicEndTime} (
                  {duration}h)
                </p>
              </div>
            </div>
          )}

          {/* Selected hours summary */}
          {selectedHours.length > 0 && (
            <div className="rounded-lg border border-green-200 bg-green-50 p-4">
              <div className="flex items-start space-x-2">
                <svg
                  className="mt-0.5 h-5 w-5 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <div>
                  <p className="font-medium text-green-800">
                    Selected Hours ({selectedHours.length} slots):
                  </p>
                  <p className="text-sm text-green-700">
                    {sortTimes(selectedHours).join(", ")}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div>
            <h4 className="mb-3 font-medium text-gray-900">
              Available Time Slots
            </h4>
            <div className="grid grid-cols-2 gap-3">
              {timeSlots.map((timeObj) => (
                <button
                  key={timeObj.time12}
                  onClick={() => toggleTimeSlot(timeObj)}
                  className={`w-full rounded-lg border-2 p-3 text-center font-medium transition-all duration-200 hover:scale-105
                    ${
                      selectedHours.includes(timeObj.time12)
                        ? "border-primaryBlue bg-primaryBlue text-white shadow-md"
                        : "border-gray-200 text-gray-700 hover:border-primaryBlue hover:bg-blue-50 hover:text-primaryBlue"
                    }
                  `}
                >
                  {timeObj.time12}
                </button>
              ))}
            </div>
          </div>
        </div>
      </RightSideModal>
    );
  };

  const renderClinicDetailsForm = () => {
    return (
      <div className="mx-auto p-4 md:container">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            Create New Clinic
          </h2>
          <p className="mt-2 text-gray-600">
            Fill in the details for your clinic and select coaches.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div className="h-fit space-y-6 rounded-xl border border-gray-100 bg-white p-6 shadow-lg">
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Clinic Information
              </h3>
              <p className="text-sm text-gray-600">
                Basic details about your clinic
              </p>
            </div>

            <div className="space-y-5">
              <div>
                <label className="mb-2 block text-sm font-semibold text-gray-700">
                  Clinic Name *
                </label>
                <input
                  type="text"
                  value={clinicName}
                  onChange={(e) => setClinicName(e.target.value)}
                  placeholder="Enter clinic name"
                  className="block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-semibold text-gray-700">
                  Maximum Players *
                </label>
                <input
                  type="number"
                  value={numberOfPlayers}
                  onChange={(e) => setNumberOfPlayers(e.target.value)}
                  placeholder="Enter max number of players"
                  min="1"
                  className="block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-semibold text-gray-700">
                  Description
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={4}
                  placeholder="Describe your clinic..."
                  className="block w-full resize-none rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-semibold text-gray-700">
                  End Date (Optional)
                </label>
                <input
                  type="date"
                  value={
                    selectedEndDate
                      ? moment(selectedEndDate).format("YYYY-MM-DD")
                      : ""
                  }
                  onChange={(e) =>
                    setSelectedEndDate(
                      e.target.value ? new Date(e.target.value) : null
                    )
                  }
                  className="block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-semibold text-gray-700">
                  Price per Person *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-4">
                    <span className="font-medium text-gray-500">$</span>
                  </div>
                  <input
                    type="number"
                    value={pricePerPerson}
                    onChange={(e) => setPricePerPerson(e.target.value)}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    className="block w-full rounded-lg border border-gray-300 py-3 pl-8 pr-4 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="h-fit w-full space-y-6 rounded-xl border border-gray-100 bg-white p-6 shadow-lg">
            <div className="border-b border-gray-200 pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Coaches
                  </h3>
                  <p className="text-sm text-gray-600">
                    Select and set hours for coaches
                  </p>
                </div>
                <span className="rounded-full bg-primaryBlue/10 px-3 py-1 text-sm font-semibold text-primaryBlue">
                  {selectedCoaches.length} selected
                </span>
              </div>
            </div>

            {selectedCoaches.length === 0 ? (
              <div className="rounded-lg border-2 border-dashed border-gray-200 p-8 text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                  <svg
                    className="h-6 w-6 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"
                    />
                  </svg>
                </div>
                <p className="mb-4 text-gray-500">No coaches selected yet</p>
                <button
                  onClick={() => setIsCoachModalOpen(true)}
                  className="inline-flex items-center rounded-lg bg-primaryBlue px-4 py-2 text-white transition-colors hover:bg-primaryBlue/90"
                >
                  <svg
                    className="mr-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  Add coaches
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                {selectedCoaches.map((coach) => (
                  <div
                    key={coach.id}
                    className="flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4 transition-all hover:shadow-sm"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white">
                        <img
                          src={coach?.user?.photo || "/default-avatar.png"}
                          alt={`${coach.user?.first_name} ${coach.user?.last_name}`}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div className="flex flex-col">
                        <span className="font-semibold text-gray-900">
                          {`${coach?.user?.first_name} ${coach?.user?.last_name}`}
                        </span>
                        <span className="text-sm text-gray-600">
                          {coach.hours.length > 0
                            ? `${coach.hours[0]} - ${
                                coach.hours[coach.hours.length - 1]
                              } (${coach.hours.length} slots)`
                            : "No hours set"}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        className="rounded-lg border border-primaryBlue bg-white px-3 py-2 text-sm font-medium text-primaryBlue transition-colors hover:bg-blue-50"
                        onClick={() => {
                          setSelectedCoachForHours(coach);
                          setIsHoursModalOpen(true);
                        }}
                      >
                        {coach.hours.length > 0 ? "Edit hours" : "Set hours"}
                      </button>
                      <button
                        onClick={() =>
                          setSelectedCoaches((prev) =>
                            prev.filter((c) => c.id !== coach.id)
                          )
                        }
                        className="rounded-lg border border-red-200 p-2 text-red-400 transition-colors hover:bg-red-50 hover:text-red-600"
                        title="Remove coach"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}

                <button
                  onClick={() => setIsCoachModalOpen(true)}
                  className="w-full rounded-lg border-2 border-dashed border-gray-300 p-3 text-center text-sm font-medium text-gray-600 transition-colors hover:border-primaryBlue hover:text-primaryBlue"
                >
                  + Add more coaches
                </button>
              </div>
            )}

            <div className="border-t border-gray-200 pt-6">
              <InteractiveButton
                onClick={handleSubmit}
                loading={isSubmitting}
                className={`w-full rounded-lg py-4 text-lg font-semibold text-white shadow-lg transition-all duration-200
                  ${
                    isSubmitting
                      ? "cursor-not-allowed bg-primaryBlue/70"
                      : "transform bg-primaryBlue hover:-translate-y-0.5 hover:bg-primaryBlue/90 hover:shadow-xl"
                  }
                `}
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <svg
                      className="h-5 w-5 animate-spin"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span>Creating Clinic...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <span>Create Clinic</span>
                    <svg
                      className="h-5 w-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                      />
                    </svg>
                  </div>
                )}
              </InteractiveButton>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const handleBackButtonClick = () => {
    if (currentStep === "details") {
      setCurrentStep("selection");
    } else {
      // Let the default back button behavior handle navigation
      // to the previous page when we're on the initial step
      return true;
    }
    return false;
  };

  console.log("selected coaches", selectedCoaches);
  return (
    <>
      <BackButton onBack={handleBackButtonClick} />

      {currentStep === "selection" ? (
        <div className="mx-auto max-w-7xl p-4">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <SportTypeSelection
              onSelectionChange={({ sport, type, subType }) => {
                setSelectedSport(sport);
                setSelectedType(type);
                setSelectedSubType(subType);
              }}
              sports={sports || []}
            />

            <div className="h-fit rounded-lg bg-white p-4 shadow-5">
              <Calendar
                currentMonth={currentMonth}
                selectedDate={selectedDate}
                onDateSelect={setSelectedDate}
                onMonthChange={setCurrentMonth}
                showNextButton={false}
                nextButtonText="Next"
                onNextMonth={handleNextMonth}
                onPreviousMonth={handlePreviousMonth}
                daysOff={club?.days_off ? JSON.parse(club.days_off) : []}
                onNextButtonClick={() => {
                  setCurrentStep("details");
                }}
              />
            </div>
            {selectedDate && (
              <TimeSlots
                onTimeClick={handleTimeClick}
                selectedDate={selectedDate}
                timeRange={selectedTimes}
                timeSlots={generateTimeSlots()}
                onNext={() => {
                  setCurrentStep("details");
                }}
                nextButtonText="Next"
                startHour={0}
                endHour={24}
                interval={30}
                className="h-fit rounded-lg bg-white p-4 shadow-5"
                multipleSlots={false}
                // onTimeSlotsChange={handleTimeSlotsChange}
                individualSelection={true}
                clubTimes={club?.times ? JSON.parse(club.times) : []}
                isTimeSlotAvailable={() => {
                  // Add your availability logic here if needed
                  return true;
                }}
              />
            )}
          </div>
        </div>
      ) : (
        renderClinicDetailsForm()
      )}
      {isCoachModalOpen && (
        <CoachSelectionModal
          coaches={coaches}
          selectedCoaches={selectedCoaches}
          setSelectedCoaches={setSelectedCoaches}
          isOpen={isCoachModalOpen}
          onClose={() => setIsCoachModalOpen(false)}
          onSave={() => setIsCoachModalOpen(false)}
        />
      )}
      <HoursSelectionModal />
    </>
  );
}
